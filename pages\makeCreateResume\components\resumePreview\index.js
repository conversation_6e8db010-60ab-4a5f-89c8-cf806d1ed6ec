// 引入API配置
const apiConfig = require('../../../../config/apiConfig');

Component({
  properties: {
    resumeData: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (!newVal) return;
        this.setData({ currentResumeData: newVal });
        this.updateTemplateData();
        // 使用防抖方式请求预览
        this.debounceRequestPreviewImage();
      }
    },
    template: {
      type: Object,
      value: {
        id: 'templateA01',
        name: '模板一'
      },
      observer: function(newVal) {
        if (newVal && newVal.id) {
          this.setData({ currentTemplate: newVal.id });
          this.updateTemplateData();
          // 模板变更时不主动请求预览
          // 由父组件统一控制请求时机
        }
      }
    },
    config: {
      type: Object,
      value: {
        themeColor: '#2B6CB0',
        fontSize: 11,
        spacing: 1.2
      },
      observer: function(newVal, oldVal) {
        if (!newVal || !oldVal) return;
        
        const needUpdate = 
          newVal.themeColor !== oldVal.themeColor ||
          newVal.fontSize !== oldVal.fontSize ||
          newVal.spacing !== oldVal.spacing;
          
        if (needUpdate) {
          this.updateStyle(newVal);
          // 配置更新时不主动请求预览
          // 由父组件统一控制请求时机
        }
      }
    }
  },

  data: {
    currentTemplate: 'templateA01',
    currentResumeData: {},
    customStyle: '',
    lastConfig: null,
    previewImageUrl: '', // 预览图片URL
    imageLoading: false, // 图片加载状态
    imageError: false, // 图片加载错误状态
    requestInProgress: false, // 是否有请求正在进行
    debounceTimer: null, // 防抖计时器
    lastRequestTimestamp: 0 // 上次请求时间戳
  },

  methods: {
    updateStyle(config) {
      if (!config) return;
      
      console.log('更新样式配置:', config);
        
      const style = `
        --theme-color: ${config.themeColor || '#4B6CB0'};
        --font-size: ${config.fontSize || 11};
        --spacing: ${config.spacing || 1};
      `;
      
      // 检查配置是否有实质性变化
      const configChanged = !this.data.lastConfig ||
        config.themeColor !== this.data.lastConfig.themeColor ||
        config.fontSize !== this.data.lastConfig.fontSize ||
        config.spacing !== this.data.lastConfig.spacing;
      
      console.log('配置是否变化:', configChanged);
      
      // 仅在配置有变化时更新
      if (configChanged) {
        this.setData({ 
          customStyle: style,
          lastConfig: { ...config }
        });
        
        this.updateTemplateData();
      }
    },
    
    updateTemplateData() {
      const { currentTemplate, currentResumeData, lastConfig } = this.data;
      if (!currentTemplate || !currentResumeData || !lastConfig) return;
      
      const template = this.selectComponent(`#${currentTemplate}`);
      if (template) {
        // 传递配置和数据到模板组件
        template.setData({
          resumeData: currentResumeData,
          config: lastConfig
        });
      }
    },

    getResumeRenderData() {
      return {
        template: {
          id: this.data.currentTemplate,
          styles: {}
        },
        resumeData: this.data.currentResumeData,
        config: this.data.lastConfig || this.properties.config,
        customStyle: this.data.customStyle
      };
    },

    // 防抖方式请求预览图片
    debounceRequestPreviewImage() {
      // 清除之前的定时器
      if (this.data.debounceTimer) {
        clearTimeout(this.data.debounceTimer);
      }

      // 设置新的定时器，延迟300ms执行
      const timer = setTimeout(() => {
        this.requestPreviewImage();
      }, 300);

      this.setData({
        debounceTimer: timer
      });
    },

    // 请求预览图片
    requestPreviewImage() {
      const { currentTemplate, currentResumeData, lastConfig, requestInProgress } = this.data;
      if (!currentTemplate || !currentResumeData || !lastConfig) return;

      // 避免重复请求
      if (requestInProgress) {
        console.log('已有请求正在进行，跳过本次请求');
        return;
      }

      // 请求节流：限制最小请求间隔为1秒
      const now = Date.now();
      if (now - this.data.lastRequestTimestamp < 1000) {
        console.log('请求过于频繁，跳过本次请求');
        return;
      }

      // 设置加载状态和请求锁
      this.setData({
        imageLoading: true,
        imageError: false,
        requestInProgress: true,
        lastRequestTimestamp: now
      });

      console.log('请求预览图片...');
      console.log('模板ID:', currentTemplate);
      console.log('简历数据:', currentResumeData);
      console.log('配置:', lastConfig);

      // 构建请求数据
      const requestData = {
        CONFIG: lastConfig,
        RESUME_DATA: currentResumeData,
          // moduleOrders: currentResumeData.moduleOrders || []
        // },
        TEMPLATE_ID: currentTemplate
      };

      // 生成一个随机参数，防止缓存
      const timestamp = Date.now();
      const randomParam = Math.floor(Math.random() * 1000000);
      const cacheParam = `t=${timestamp}&r=${randomParam}`;

      // 发送请求
      wx.request({
        url: `${apiConfig.exportJpegUrl}?${cacheParam}`,
        method: 'POST',
        responseType: 'arraybuffer',
        header: {
          'Content-Type': 'application/json'
        },
        data: requestData,
        success: (res) => {
          if (res.statusCode !== 200) {
            console.error('预览图片请求失败:', res);
            this.setData({
              imageLoading: false,
              imageError: true,
              requestInProgress: false
            });
            return;
          }

          // 处理二进制图片数据
          const buffer = res.data;
          // 检查文件头确保是图片（JPEG格式）
          const header = new Uint8Array(buffer).slice(0, 2);
          const isJpeg = header[0] === 0xFF && header[1] === 0xD8;

          if (!isJpeg) {
            console.error('服务器返回的不是有效的JPEG图片');
            this.setData({
              imageLoading: false,
              imageError: true,
              requestInProgress: false
            });
            return;
          }

          // 创建临时文件
          const fs = wx.getFileSystemManager();
          // const tempFilePath = `${wx.env.USER_DATA_PATH}/preview_${timestamp}.jpg`;
          const tempFilePath = `${wx.env.USER_DATA_PATH}/createResume_preview_resume.jpg`;

          try {
            fs.writeFileSync(tempFilePath, buffer, 'binary');
            console.log('预览图片临时文件创建成功:', tempFilePath);

            this.setData({
              previewImageUrl: tempFilePath,
              imageLoading: false,
              requestInProgress: false
            });
          } catch (error) {
            console.error('预览图片文件写入失败:', error);
            this.setData({
              imageLoading: false,
              imageError: true,
              requestInProgress: false
            });
          }
        },
        fail: (error) => {
          console.error('预览图片请求失败:', error);
          this.setData({
            imageLoading: false,
            imageError: true,
            requestInProgress: false
          });
        }
      });
    },

    // 图片加载错误处理
    onImageError(e) {
      console.error('图片加载错误:', e);
      this.setData({
        previewImageUrl: '',
        imageError: true,
        imageLoading: false
      });
    }
  }
}); 