/**
 * 简历相关API接口
 */
const request = require('./request');

/**
 * 生成简历预览图片
 * @param {Object} resumeData 简历数据
 * @param {Object} config 配置信息
 * @param {string} templateId 模板ID
 * @returns {Promise<ArrayBuffer>} 图片二进制数据
 */
function generatePreviewImage(resumeData, config, templateId) {
  const requestData = {
    CONFIG: config,
    RESUME_DATA: resumeData,
    TEMPLATE_ID: templateId
  };

  // 生成缓存参数，防止缓存
  const timestamp = Date.now();
  const randomParam = Math.floor(Math.random() * 1000000);
  const cacheParam = `t=${timestamp}&r=${randomParam}`;

  return request.request({
    url: `/resume/export-jpeg?${cacheParam}`,
    // url: `/resume/export-jpeg`,
    method: 'POST',
    data: requestData,
    header: {
      'Content-Type': 'application/json'
    },
    responseType: 'arraybuffer',
    showLoading: false,
    showError: false,
    needAuth: true,
  });
}

/**
 * 生成PDF文件
 * @param {Object} resumeData 简历数据
 * @param {Object} config 配置信息
 * @param {string} templateId 模板ID
 * @returns {Promise<ArrayBuffer>} PDF二进制数据
 */
function generatePDF(resumeData, config, templateId) {
  const requestData = {
    CONFIG: config,
    RESUME_DATA: resumeData,
    TEMPLATE_ID: templateId
  };

  return request.request({
    url: '/resume/export-pdf',
    method: 'POST',
    data: requestData,
    header: {
      'Content-Type': 'application/json'
    },
    responseType: 'arraybuffer',
    showLoading: true,
    loadingText: '正在生成PDF...',
    showError: true,
    needAuth: true,
  });
}

/**
 * 渲染简历模板
 * @param {Object} resumeData 简历数据
 * @param {Object} config 配置信息
 * @param {string} templateId 模板ID
 * @returns {Promise<Object>} 渲染结果
 */
function renderTemplate(resumeData, config, templateId) {
  const requestData = {
    CONFIG: config,
    RESUME_DATA: resumeData,
    TEMPLATE_ID: templateId
  };

  return request.post('/resume/render', requestData, {
    showLoading: true,
    loadingText: '渲染中...'
  });
}

/**
 * 保存简历到云端
 * @param {Object} resumeData 简历数据
 * @param {string} resumeId 简历ID
 * @returns {Promise<Object>} 保存结果
 */
function saveResume(resumeData, resumeId) {
  return request.post('/resume/save', {
    resumeData,
    resumeId
  }, {
    showLoading: true,
    loadingText: '保存中...'
  });
}

/**
 * 获取用户简历列表
 * @returns {Promise<Object>} 简历列表
 */
function getResumeList() {
  return request.get('/resume/list', {}, {
    showLoading: true,
    loadingText: '加载中...'
  });
}

/**
 * 获取指定简历
 * @param {string} resumeId 简历ID
 * @returns {Promise<Object>} 简历数据
 */
function getResume(resumeId) {
  return request.get('/resume/get', {
    resumeId
  }, {
    showLoading: true,
    loadingText: '加载中...'
  });
}

/**
 * 删除简历
 * @param {string} resumeId 简历ID
 * @returns {Promise<Object>} 删除结果
 */
function deleteResume(resumeId) {
  return request.post('/resume/delete', {
    resumeId
  }, {
    showLoading: true,
    loadingText: '删除中...'
  });
}

module.exports = {
  generatePreviewImage,
  generatePDF,
  renderTemplate,
  saveResume,
  getResumeList,
  getResume,
  deleteResume
};
