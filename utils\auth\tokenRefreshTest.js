/**
 * Token刷新测试工具
 * 用于测试和验证token自动刷新功能
 */

const tokenManager = require('./tokenManager');
const request = require('../api/request');

/**
 * 测试token状态
 */
function testTokenStatus() {
  console.log('=== Token状态测试 ===');
  
  const tokenInfo = tokenManager.getTokenInfo();
  console.log('当前token信息:', tokenInfo);
  
  const status = tokenManager.getTokenStatus();
  console.log('Token状态:', status);
  
  const isExpiring = tokenManager.isTokenExpiringSoon();
  console.log('是否即将过期:', isExpiring);
  
  const needsLogin = tokenManager.needsLogin();
  console.log('是否需要登录:', needsLogin);
}

/**
 * 测试token刷新
 */
async function testTokenRefresh() {
  console.log('=== Token刷新测试 ===');
  
  try {
    console.log('开始测试token刷新...');
    const newToken = await tokenManager.refreshToken();
    console.log('Token刷新成功:', newToken);
    
    // 验证新token
    const newTokenInfo = tokenManager.getTokenInfo();
    console.log('新token信息:', newTokenInfo);
    
  } catch (error) {
    console.error('Token刷新失败:', error);
  }
}

/**
 * 测试自动重新登录
 */
async function testAutoReLogin() {
  console.log('=== 自动重新登录测试 ===');
  
  try {
    console.log('开始测试自动重新登录...');
    const newToken = await tokenManager.autoReLogin();
    console.log('自动重新登录成功:', newToken);
    
  } catch (error) {
    console.error('自动重新登录失败:', error);
  }
}

/**
 * 测试获取有效token
 */
async function testGetValidToken() {
  console.log('=== 获取有效Token测试 ===');
  
  try {
    console.log('开始获取有效token...');
    const validToken = await tokenManager.getValidToken();
    console.log('获取有效token成功:', validToken);
    
  } catch (error) {
    console.error('获取有效token失败:', error);
  }
}

/**
 * 测试API请求自动刷新
 */
async function testApiRequestWithRefresh() {
  console.log('=== API请求自动刷新测试 ===');
  
  try {
    console.log('发起需要认证的API请求...');
    const response = await request.get('/auth/user');
    console.log('API请求成功:', response);
    
  } catch (error) {
    console.error('API请求失败:', error);
  }
}

/**
 * 模拟token过期
 */
function simulateTokenExpiry() {
  console.log('=== 模拟Token过期 ===');
  
  const tokenInfo = tokenManager.getTokenInfo();
  if (tokenInfo) {
    // 将创建时间设置为很久以前，模拟过期
    const expiredTokenInfo = {
      ...tokenInfo,
      created_at: Date.now() - (2 * 60 * 60 * 1000), // 2小时前
      expires_in: 1800 // 30分钟有效期
    };
    
    wx.setStorageSync('tokenInfo', expiredTokenInfo);
    console.log('已模拟token过期');
    
    // 测试状态
    testTokenStatus();
  } else {
    console.log('没有token信息可以模拟过期');
  }
}

/**
 * 清除token信息
 */
function clearTokenForTest() {
  console.log('=== 清除Token测试 ===');
  
  tokenManager.clearTokenInfo();
  console.log('Token信息已清除');
  
  // 测试状态
  testTokenStatus();
}

/**
 * 运行完整测试套件
 */
async function runFullTestSuite() {
  console.log('🚀 开始运行Token管理完整测试套件...');
  
  try {
    // 1. 测试当前状态
    testTokenStatus();
    
    // 2. 测试获取有效token
    await testGetValidToken();
    
    // 3. 测试API请求
    await testApiRequestWithRefresh();
    
    // 4. 模拟token过期并测试刷新
    simulateTokenExpiry();
    await testGetValidToken();
    
    // 5. 清除token并测试自动登录
    clearTokenForTest();
    await testGetValidToken();
    
    console.log('✅ 测试套件运行完成');
    
  } catch (error) {
    console.error('❌ 测试套件运行失败:', error);
  }
}

/**
 * 监控token状态变化
 */
function startTokenMonitoring() {
  console.log('🔍 开始监控Token状态...');
  
  const monitorInterval = setInterval(() => {
    const status = tokenManager.getTokenStatus();
    const isExpiring = tokenManager.isTokenExpiringSoon();
    const tokenInfo = tokenManager.getTokenInfo();
    
    console.log('Token监控:', {
      status,
      isExpiring,
      hasToken: !!tokenInfo,
      expiresIn: tokenInfo ? Math.floor((tokenInfo.created_at + tokenInfo.expires_in * 1000 - Date.now()) / 1000) : 0
    });
    
  }, 30000); // 每30秒检查一次
  
  // 返回清理函数
  return () => {
    clearInterval(monitorInterval);
    console.log('Token监控已停止');
  };
}

module.exports = {
  testTokenStatus,
  testTokenRefresh,
  testAutoReLogin,
  testGetValidToken,
  testApiRequestWithRefresh,
  simulateTokenExpiry,
  clearTokenForTest,
  runFullTestSuite,
  startTokenMonitoring
};
