/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "SimHei", sans-serif;
}

body {
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    padding: 20px;
}

.resume-container {
    width: 210mm;
    min-height: 297mm;
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
}

/* 左侧栏样式 */
.left-column {
    width: 30%;
    background-color: #f0f0f0;
    padding-bottom: 20px;
}

.photo-container {
    padding: 20px;
    display: flex;
    justify-content: center;
}

.photo-container img {
    width: 150px;
    height: 200px;
    object-fit: cover;
    border: 1px solid #ddd;
}

.name-container {
    text-align: center;
    padding: 10px 0;
    background-color: #fff;
    margin-bottom: 20px;
}

.name-container h1 {
    font-size: 28px;
    font-weight: bold;
}

/* 右侧栏样式 */
.right-column {
    width: 70%;
    padding: 20px;
}

/* 通用部分样式 */
.section {
    margin-bottom: 20px;
}

.section-title {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    background-color: #3a4a5a;
    color: white;
    border-radius: 4px;
    margin-bottom: 10px;
}

.section-title i {
    margin-right: 10px;
    font-size: 18px;
}

.section-title h2 {
    font-size: 18px;
    font-weight: normal;
}

.section-content {
    padding: 0 15px;
}

.left-column .section-content {
    font-size: 14px;
    line-height: 1.8;
}

.left-column .section-content p {
    display: flex;
}

.left-column .section-content p span:first-child {
    width: 90px;
    font-weight: bold;
}

/* 教育背景样式 */
.education-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-weight: bold;
}

.education-courses {
    line-height: 1.6;
}

/* 在校经历样式 */
.experience-item {
    margin-bottom: 15px;
    position: relative;
    padding-left: 20px;
}

.experience-item:before {
    content: "";
    position: absolute;
    left: 0;
    top: 5px;
    width: 8px;
    height: 8px;
    background-color: #3a4a5a;
    border-radius: 50%;
}

/* 技能证书样式 */
#skills-section .section-content p {
    margin-bottom: 10px;
    line-height: 1.6;
}

/* 自我评价样式 */
.evaluation-item {
    margin-bottom: 15px;
    position: relative;
    padding-left: 20px;
}

.evaluation-item:before {
    content: "";
    position: absolute;
    left: 0;
    top: 5px;
    width: 8px;
    height: 8px;
    background-color: #3a4a5a;
    border-radius: 50%;
}

/* 隐藏空模块 */
.hidden {
    display: none;
} 