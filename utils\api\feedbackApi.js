/**
 * 反馈相关API接口
 */
const request = require('./request');

/**
 * 提交用户反馈
 * @param {Object} feedbackData 反馈数据
 * @param {string} feedbackData.type 反馈类型
 * @param {string} feedbackData.content 反馈内容
 * @param {string} feedbackData.contact 联系方式
 * @param {Object} feedbackData.deviceInfo 设备信息
 */
function submitFeedback(feedbackData) {
  return request.post('/feedback/submit', {
    ...feedbackData,
    timestamp: Date.now()
  }, {
    showLoading: true,
    loadingText: '提交中...'
  });
}

/**
 * 获取反馈列表（如果需要用户查看自己的反馈）
 */
function getFeedbackList() {
  return request.get('/feedback/list', {}, {
    showLoading: true
  });
}

module.exports = {
  submitFeedback,
  getFeedbackList
};
