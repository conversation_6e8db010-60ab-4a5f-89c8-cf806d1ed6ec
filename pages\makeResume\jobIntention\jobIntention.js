Page({
  data: {
    jobIntentionFormData: {
      position: '',    // 期望职位
      location: '',    // 期望地点
      salary: '',      // 期望薪资
      status: ''       // 求职状态
    },
    salaryRange: [
      '3k以下', '3-5k', '5-7k', '7-10k',
      '10-15k', '15-20k', '20-30k', '30-50k',
      '50k以上', '面议'
    ]
  },

  onLoad() {
    // 加载已保存的数据
    const savedData = wx.getStorageSync('jobIntention') || {};
    if (savedData) {
      this.setData({
        jobIntentionFormData: {
          ...this.data.jobIntentionFormData,
          ...savedData
        }
      });
    }
  },

  // 输入框内容变化时触发
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`jobIntentionFormData.${field}`]: value
    });
  },

  // 薪资选择器变化时触发
  handleSalaryChange(e) {
    const value = this.data.salaryRange[e.detail.value];
    this.setData({
      'jobIntentionFormData.salary': value
    });
  },

  // 清除字段内容
  clearField(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`jobIntentionFormData.${field}`]: ''
    });
  },

  // 保存信息
  saveInfo() {
    console.log('========== 求职意向保存开始 ==========');
    console.log('保存的数据:', this.data.jobIntentionFormData);

    // 保存到原有的storage键（兼容性）
    wx.setStorageSync('jobIntention', this.data.jobIntentionFormData);

    // 同时保存到resumeManager
    const resumeManager = require('../../../utils/resume/resumeManager.js');
    const currentResumeData = resumeManager.getCurrentResumeData() || {};

    // 更新求职意向
    currentResumeData.jobIntention = this.data.jobIntentionFormData;

    // 保存到resumeManager
    const success = resumeManager.saveCurrentResumeData(currentResumeData);
    console.log('求职意向保存到resumeManager结果:', success);

    wx.showToast({
      title: '保存成功',
      icon: 'success'
    });
    wx.navigateBack();
  },

  // 删除信息
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除求职意向信息吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            jobIntentionFormData: {
              position: '',
              location: '',
              salary: '',
              status: ''
            }
          });
          wx.setStorageSync('jobIntention', null);
          wx.navigateBack();
        }
      }
    });
  }
});