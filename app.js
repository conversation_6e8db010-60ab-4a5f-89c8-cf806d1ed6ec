// app.js
const userTracker = require('./utils/user/userTracker');
const loginInterceptor = require('./utils/user/loginInterceptor');

App({
  globalData: {
    userInfo: null,
    hasUserInfo: false,
    userId: null,
    userToken: null
  },

  onLaunch() {
    // 检查登录状态
    this.checkLoginStatus();

    // 获取系统信息
    this.getSystemInfo();
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    const userId = wx.getStorageSync('userId');
    const userToken = wx.getStorageSync('userToken');

    if (userInfo && userId && userToken) {
      this.globalData.userInfo = userInfo;
      this.globalData.hasUserInfo = true;
      this.globalData.userId = userId;
      this.globalData.userToken = userToken;

      console.log('用户已登录:', userId);
    } else {
      this.globalData.hasUserInfo = false;
      this.globalData.userInfo = null;
      this.globalData.userId = null;
      this.globalData.userToken = null;

      console.log('用户未登录');
    }
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
      console.log('系统信息:', systemInfo);
    } catch (e) {
      console.error('获取系统信息失败:', e);
    }
  },

  // 用户登录
  login(userInfo, userId, token) {
    // 更新全局数据
    this.globalData.userInfo = userInfo;
    this.globalData.hasUserInfo = true;
    this.globalData.userId = userId;
    this.globalData.userToken = token;

    // 保存到本地存储
    wx.setStorageSync('userInfo', userInfo);
    wx.setStorageSync('userId', userId);
    wx.setStorageSync('userToken', token);

    console.log('用户登录成功:', userId);
  },

  // 用户登出
  logout() {
    // 清除全局数据
    this.globalData.userInfo = null;
    this.globalData.hasUserInfo = false;
    this.globalData.userId = null;
    this.globalData.userToken = null;

    // 清除本地存储
    loginInterceptor.clearLoginInfo();

    console.log('用户已登出');
  },

  // 记录用户行为
  trackUserAction(actionType, actionDetail = {}, immediate = false) {
    userTracker.trackUserAction(actionType, actionDetail, immediate);
  },

  // 检查是否登录，未登录则跳转到登录页面
  checkLogin(redirectUrl) {
    return loginInterceptor.checkLoginStatus(redirectUrl);
  },

  // 获取用户ID
  getUserId() {
    return this.globalData.userId || loginInterceptor.getUserId();
  }
})
