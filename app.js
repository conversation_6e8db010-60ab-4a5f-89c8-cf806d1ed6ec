// app.js
const userTracker = require('./utils/user/userTracker');
const loginInterceptor = require('./utils/user/loginInterceptor');
const autoLogin = require('./utils/user/autoLogin');
const userState = require('./utils/user/userState');

App({
  globalData: {
    userInfo: null,
    hasUserInfo: false,
    userId: null,
    userToken: null,
    isMember: false, // 添加会员状态
    membershipExpiry: null // 会员到期时间
  },

  onLaunch() {
    // 获取系统信息
    this.getSystemInfo();

    // 同步用户状态
    userState.syncUserState();

    // 执行自动登录
    autoLogin.performAutoLogin();
  },

  // 执行自动登录
  // performAutoLogin() {
  //   autoLogin.silentLogin()
  //     .then((success) => {
  //       if (success) {
  //         console.log('自动登录成功');
  //         this.updateGlobalUserData();
  //       } else {
  //         console.log('自动登录失败，用户需要手动登录');
  //       }
  //     })
  //     .catch((error) => {
  //       console.error('自动登录过程中发生错误:', error);
  //     });
  // },

  // 更新全局用户数据
  updateGlobalUserData() {
    const userId = wx.getStorageSync('userId');
    const userToken = wx.getStorageSync('userToken');
    const membershipInfo = wx.getStorageSync('membershipInfo');

    this.globalData.userInfo = null; // 不再存储用户信息
    this.globalData.hasUserInfo = !!(userId && userToken);
    this.globalData.userId = userId;
    this.globalData.userToken = userToken;

    // 更新会员状态
    if (membershipInfo) {
      this.globalData.isMember = membershipInfo.isMember || false;
      this.globalData.membershipExpiry = membershipInfo.expiry || null;
    }

    console.log('全局用户数据已更新:', {
      hasUserInfo: this.globalData.hasUserInfo,
      userId: userId,
      isMember: this.globalData.isMember
    });
  },

  // 检查登录状态（保留原有方法以兼容现有代码）
  // checkLoginStatus() {
  //   return loginInterceptor.checkLoginStatus();
  // },

  // 获取系统信息
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
      console.log('系统信息:', systemInfo);
    } catch (e) {
      console.error('获取系统信息失败:', e);
    }
  },

  // 用户登录
  login(userInfo, userId, token) {
    // 更新全局数据
    this.globalData.userInfo = userInfo;
    this.globalData.hasUserInfo = true;
    this.globalData.userId = userId;
    this.globalData.userToken = token;

    // 保存到本地存储
    wx.setStorageSync('userInfo', userInfo);
    wx.setStorageSync('userId', userId);
    wx.setStorageSync('userToken', token);

    console.log('用户登录成功:', userId);
  },

  // 用户登出
  logout() {
    // 清除全局数据
    this.globalData.userInfo = null;
    this.globalData.hasUserInfo = false;
    this.globalData.userId = null;
    this.globalData.userToken = null;

    // 清除本地存储
    loginInterceptor.clearLoginInfo();

    console.log('用户已登出');
  },

  // 记录用户行为
  trackUserAction(actionType, actionDetail = {}, immediate = false) {
    userTracker.trackUserAction(actionType, actionDetail, immediate);
  },

  // 检查是否登录，未登录则跳转到登录页面
  checkLogin(redirectUrl) {
    return loginInterceptor.checkLoginStatus(redirectUrl);
  },

  // 获取用户ID
  getUserId() {
    return this.globalData.userId || loginInterceptor.getUserId();
  }
})
