// pages/user/resumes/resumes.js
const apiConfig = require('../../../config/apiConfig');
const app = getApp();

Page({
  data: {
    resumeList: [],
    isLoading: false,
    isEmpty: false
  },

  onLoad() {
    // 检查登录状态
    if (!app.checkLogin('/pages/user/resumes/resumes')) {
      return;
    }
    
    // 加载简历列表
    this.loadResumeList();
  },

  onShow() {
    // 每次显示页面时刷新数据
    if (app.globalData.hasUserInfo) {
      this.loadResumeList();
    }
  },

  // 加载简历列表
  loadResumeList() {
    const userId = app.getUserId();
    if (!userId) return;
    
    this.setData({ isLoading: true });
    
    wx.request({
      url: apiConfig.listResumesUrl,
      method: 'GET',
      data: { userId: userId },
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('userToken')}`
      },
      success: (res) => {
        if (res.data.success) {
          const resumeList = res.data.resumes || [];
          this.setData({
            resumeList: resumeList,
            isEmpty: resumeList.length === 0
          });
          
          // 记录用户行为
          app.trackUserAction('view_resume_list');
        } else {
          console.error('获取简历列表失败:', res.data.message);
          wx.showToast({
            title: '获取简历列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求简历列表失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
        wx.stopPullDownRefresh();
      }
    });
  },

  // 创建新简历
  createNewResume() {
    wx.navigateTo({
      url: '/pages/makeResume/makeResume'
    });
  },

  // 编辑简历
  editResume(e) {
    const resumeId = e.currentTarget.dataset.id;
    
    // 记录用户行为
    app.trackUserAction('edit_resume', { resumeId: resumeId });
    
    // 先获取简历数据
    this.getResumeData(resumeId, (resumeData) => {
      // 将数据保存到本地存储
      this.saveResumeDataToStorage(resumeData);
      
      // 跳转到简历编辑页面
      wx.navigateTo({
        url: '/pages/makeResume/makeResume'
      });
    });
  },

  // 预览简历
  previewResume(e) {
    const resumeId = e.currentTarget.dataset.id;
    
    // 记录用户行为
    app.trackUserAction('preview_resume', { resumeId: resumeId });
    
    // 先获取简历数据
    this.getResumeData(resumeId, (resumeData) => {
      // 将数据转换为查询字符串
      const queryString = encodeURIComponent(JSON.stringify(resumeData));
      
      // 跳转到预览页面
      wx.navigateTo({
        url: `/pages/makeCreateResume/makeCreateResume?resumeData=${queryString}`
      });
    });
  },

  // 删除简历
  deleteResume(e) {
    const resumeId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '提示',
      content: '确定要删除此简历吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ isLoading: true });
          
          wx.request({
            url: apiConfig.deleteResumeUrl,
            method: 'POST',
            data: { 
              userId: app.getUserId(),
              resumeId: resumeId
            },
            header: {
              'Authorization': `Bearer ${wx.getStorageSync('userToken')}`
            },
            success: (res) => {
              if (res.data.success) {
                // 记录用户行为
                app.trackUserAction('delete_resume', { resumeId: resumeId });
                
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                // 重新加载列表
                this.loadResumeList();
              } else {
                console.error('删除简历失败:', res.data.message);
                wx.showToast({
                  title: '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              console.error('删除简历请求失败:', err);
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            },
            complete: () => {
              this.setData({ isLoading: false });
            }
          });
        }
      }
    });
  },

  // 获取简历数据
  getResumeData(resumeId, callback) {
    wx.showLoading({ title: '加载中...' });
    
    wx.request({
      url: apiConfig.getResumeUrl,
      method: 'GET',
      data: { 
        userId: app.getUserId(),
        resumeId: resumeId
      },
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('userToken')}`
      },
      success: (res) => {
        if (res.data.success && res.data.resumeData) {
          callback(res.data.resumeData);
        } else {
          console.error('获取简历数据失败:', res.data.message);
          wx.showToast({
            title: '获取简历数据失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求简历数据失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 将简历数据保存到本地存储
  saveResumeDataToStorage(resumeData) {
    // 保存各模块数据到本地存储
    if (resumeData.basicInfo) wx.setStorageSync('basicInfo', resumeData.basicInfo);
    if (resumeData.jobIntention) wx.setStorageSync('jobIntention', resumeData.jobIntention);
    if (resumeData.education) wx.setStorageSync('education', resumeData.education);
    if (resumeData.school) wx.setStorageSync('schoolList', resumeData.school);
    if (resumeData.internship) wx.setStorageSync('internshipList', resumeData.internship);
    if (resumeData.work) wx.setStorageSync('workList', resumeData.work);
    if (resumeData.project) wx.setStorageSync('projectList', resumeData.project);
    if (resumeData.skills) wx.setStorageSync('skillsList', resumeData.skills);
    if (resumeData.awards) wx.setStorageSync('awardsList', resumeData.awards);
    if (resumeData.interests) wx.setStorageSync('interestsList', resumeData.interests);
    if (resumeData.evaluation) wx.setStorageSync('evaluationList', resumeData.evaluation);
    if (resumeData.custom1) wx.setStorageSync('customList1', resumeData.custom1);
    if (resumeData.custom2) wx.setStorageSync('customList2', resumeData.custom2);
    if (resumeData.custom3) wx.setStorageSync('customList3', resumeData.custom3);
    
    // 保存模块顺序
    if (resumeData.moduleOrders) wx.setStorageSync('moduleOrders', resumeData.moduleOrders);
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadResumeList();
  }
})
