/**
 * 全局用户状态管理工具
 * 提供统一的用户状态获取和设置方法
 */

/**
 * 获取用户Token
 * @returns {string|null} 用户Token
 */
function getUserToken() {
  // 优先从全局数据获取
  const app = getApp();
  if (app && app.globalData && app.globalData.userToken) {
    return app.globalData.userToken;
  }
  
  // 从本地存储获取
  const token = wx.getStorageSync('userToken');
  
  // 同步到全局数据
  if (token && app && app.globalData) {
    app.globalData.userToken = token;
  }
  
  return token || null;
}

/**
 * 获取用户ID
 * @returns {string|null} 用户ID
 */
function getUserId() {
  // 优先从全局数据获取
  const app = getApp();
  if (app && app.globalData && app.globalData.userId) {
    return app.globalData.userId;
  }
  
  // 从本地存储获取
  const userId = wx.getStorageSync('userId');
  
  // 同步到全局数据
  if (userId && app && app.globalData) {
    app.globalData.userId = userId;
  }
  
  return userId || null;
}

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
function isLoggedIn() {
  const token = getUserToken();
  const userId = getUserId();
  return !!(token && userId);
}

/**
 * 获取会员信息
 * @returns {Object|null} 会员信息
 */
function getMembershipInfo() {
  const app = getApp();
  
  // 优先从全局数据获取
  if (app && app.globalData) {
    if (app.globalData.isMember !== undefined) {
      return {
        isMember: app.globalData.isMember,
        expiry: app.globalData.membershipExpiry
      };
    }
  }
  
  // 从本地存储获取
  const membershipInfo = wx.getStorageSync('membershipInfo');
  
  // 同步到全局数据
  if (membershipInfo && app && app.globalData) {
    app.globalData.isMember = membershipInfo.isMember || false;
    app.globalData.membershipExpiry = membershipInfo.expiry || null;
  }
  
  return membershipInfo || { isMember: false, expiry: null };
}

/**
 * 检查用户是否为会员
 * @returns {boolean} 是否为会员
 */
function isMember() {
  const membershipInfo = getMembershipInfo();
  
  if (!membershipInfo.isMember) {
    return false;
  }
  
  // 如果有到期时间，检查是否过期
  if (membershipInfo.expiry) {
    const expiryDate = new Date(membershipInfo.expiry);
    const now = new Date();
    return expiryDate > now;
  }
  
  // 没有到期时间表示永久会员
  return true;
}

/**
 * 设置用户登录信息
 * @param {string} token 用户Token
 * @param {string} userId 用户ID
 * @param {Object} membershipInfo 会员信息
 */
function setUserLoginInfo(token, userId, membershipInfo = null) {
  // 保存到本地存储
  wx.setStorageSync('userToken', token);
  wx.setStorageSync('userId', userId);
  
  if (membershipInfo) {
    wx.setStorageSync('membershipInfo', membershipInfo);
  }
  
  // 更新全局状态
  const app = getApp();
  if (app && app.globalData) {
    app.globalData.userToken = token;
    app.globalData.userId = userId;
    app.globalData.hasUserInfo = true;
    
    if (membershipInfo) {
      app.globalData.isMember = membershipInfo.isMember || false;
      app.globalData.membershipExpiry = membershipInfo.expiry || null;
    }
  }
  
  console.log('用户登录信息已设置:', { userId, isMember: isMember() });
}

/**
 * 清除用户登录信息
 */
function clearUserLoginInfo() {
  // 清除本地存储
  wx.removeStorageSync('userToken');
  wx.removeStorageSync('userId');
  wx.removeStorageSync('membershipInfo');
  
  // 清除全局状态
  const app = getApp();
  if (app && app.globalData) {
    app.globalData.userToken = null;
    app.globalData.userId = null;
    app.globalData.hasUserInfo = false;
    app.globalData.isMember = false;
    app.globalData.membershipExpiry = null;
  }
  
  console.log('用户登录信息已清除');
}

/**
 * 更新会员信息
 * @param {Object} membershipInfo 会员信息
 */
function updateMembershipInfo(membershipInfo) {
  // 保存到本地存储
  wx.setStorageSync('membershipInfo', membershipInfo);
  
  // 更新全局状态
  const app = getApp();
  if (app && app.globalData) {
    app.globalData.isMember = membershipInfo.isMember || false;
    app.globalData.membershipExpiry = membershipInfo.expiry || null;
  }
  
  console.log('会员信息已更新:', membershipInfo);
}

/**
 * 获取用户状态摘要
 * @returns {Object} 用户状态摘要
 */
function getUserStateSummary() {
  return {
    isLoggedIn: isLoggedIn(),
    userId: getUserId(),
    hasToken: !!getUserToken(),
    isMember: isMember(),
    membershipInfo: getMembershipInfo()
  };
}

/**
 * 同步本地存储和全局状态
 */
function syncUserState() {
  const token = wx.getStorageSync('userToken');
  const userId = wx.getStorageSync('userId');
  const membershipInfo = wx.getStorageSync('membershipInfo');
  
  const app = getApp();
  if (app && app.globalData) {
    app.globalData.userToken = token || null;
    app.globalData.userId = userId || null;
    app.globalData.hasUserInfo = !!(token && userId);
    
    if (membershipInfo) {
      app.globalData.isMember = membershipInfo.isMember || false;
      app.globalData.membershipExpiry = membershipInfo.expiry || null;
    } else {
      app.globalData.isMember = false;
      app.globalData.membershipExpiry = null;
    }
  }
  
  console.log('用户状态已同步:', getUserStateSummary());
}

module.exports = {
  getUserToken,
  getUserId,
  isLoggedIn,
  getMembershipInfo,
  isMember,
  setUserLoginInfo,
  clearUserLoginInfo,
  updateMembershipInfo,
  getUserStateSummary,
  syncUserState
};
