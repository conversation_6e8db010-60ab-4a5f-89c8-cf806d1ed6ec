<!-- pages/user/resumes/resumes.wxml -->
<view class="container">
  <!-- 顶部操作栏 -->
  <view class="action-bar">
    <button class="create-btn" bindtap="createNewResume">
      <text class="icon">+</text>
      <text>新建简历</text>
    </button>
  </view>
  
  <!-- 简历列表 -->
  <view class="resume-list" wx:if="{{!isEmpty}}">
    <view class="resume-item" wx:for="{{resumeList}}" wx:key="id">
      <view class="resume-info">
        <view class="resume-title">{{item.title || '未命名简历'}}</view>
        <view class="resume-meta">
          <text class="resume-date">创建时间: {{item.createTime}}</text>
          <text class="resume-date">更新时间: {{item.updateTime}}</text>
        </view>
      </view>
      
      <view class="resume-actions">
        <view class="action-btn preview-btn" bindtap="previewResume" data-id="{{item.id}}">
          <text class="action-icon">👁️</text>
          <text>预览</text>
        </view>
        <view class="action-btn edit-btn" bindtap="editResume" data-id="{{item.id}}">
          <text class="action-icon">✏️</text>
          <text>编辑</text>
        </view>
        <view class="action-btn delete-btn" bindtap="deleteResume" data-id="{{item.id}}">
          <text class="action-icon">🗑️</text>
          <text>删除</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isEmpty && !isLoading}}">
    <image class="empty-icon" src="/pages/index/images/empty.png" mode="aspectFit"></image>
    <text class="empty-text">暂无简历，点击"新建简历"开始创建</text>
    <button class="create-btn-large" bindtap="createNewResume">新建简历</button>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
