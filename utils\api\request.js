/**
 * 统一的API请求工具
 * 自动处理token、userId、loading、错误等
 */
const apiConfig = require('../../config/apiConfig');

/**
 * 发起API请求
 * @param {Object} options 请求配置
 * @param {string} options.url 请求地址（相对路径或完整URL）
 * @param {string} options.method 请求方法，默认GET
 * @param {Object} options.data 请求数据
 * @param {Object} options.header 额外的请求头
 * @param {boolean} options.showLoading 是否显示loading，默认true
 * @param {string} options.loadingText loading文字，默认"加载中..."
 * @param {boolean} options.showError 是否显示错误提示，默认true
 * @param {boolean} options.needAuth 是否需要认证，默认true
 * @param {string} options.responseType 响应类型，默认为空
 * @returns {Promise} 请求Promise
 */
function request(options = {}) {
  return new Promise((resolve, reject) => {
    const {
      url,
      method = 'GET',
      data = {},
      header = {},
      showLoading = true,
      loadingText = '加载中...',
      showError = true,
      needAuth = true,
      responseType
    } = options;

    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : getBaseUrl() + url;

    // 构建请求头
    let requestHeader = {
      'Content-Type': 'application/json',
      ...header
    };

    // 添加认证信息
    if (needAuth) {
      const authHeader = getAuthHeader();
      console.log('认证头:', authHeader);
      requestHeader = { ...requestHeader, ...authHeader };
      // Object.assign(requestHeader, authHeader);
    }

    // 显示loading
    if (showLoading) {
      wx.showLoading({
        title: loadingText,
        mask: true
      });
    }

    // 构建请求配置
    const requestConfig = {
      url: fullUrl,
      method: method.toUpperCase(),
      data: data,
      header: requestHeader,
      success: (res) => {
        console.log(`API请求成功 [${method.toUpperCase()}] ${fullUrl}:`, res.data);

        // 如果是arraybuffer类型，直接返回数据
        if (responseType === 'arraybuffer') {
          resolve(res.data);
          return;
        }

        // 处理业务逻辑
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          // 根据服务端API文档，错误信息在detail字段
          const errorMsg = res.data?.detail || res.data?.message || `请求失败 (${res.statusCode})`;
          console.error(`API业务错误 [${method.toUpperCase()}] ${fullUrl}:`, errorMsg);

          if (showError) {
            wx.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 2000
            });
          }

          reject(new Error(errorMsg));
        }
      },
      fail: (err) => {
        console.error(`API请求失败 [${method.toUpperCase()}] ${fullUrl}:`, err);

        const errorMsg = getErrorMessage(err);

        if (showError) {
          wx.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          });
        }

        reject(err);
      },
      complete: () => {
        if (showLoading) {
          wx.hideLoading();
        }
      }
    };

    // 添加响应类型
    if (responseType) {
      requestConfig.responseType = responseType;
    }

    // 发起请求
    console.log('发起请求的配置:', requestConfig);
    wx.request(requestConfig);
  });
}

/**
 * GET请求
 */
function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  });
}

/**
 * POST请求
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
}

/**
 * PUT请求
 */
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
}

/**
 * DELETE请求
 */
function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  });
}

/**
 * 获取基础URL
 */
function getBaseUrl() {
  // 根据环境返回不同的基础URL
  const app = getApp();
  if (app && app.globalData && app.globalData.env === 'development') {
    return apiConfig.devBaseUrl || apiConfig.baseUrl;
  }
  return apiConfig.baseUrl;
}

/**
 * 获取认证请求头
 */
function getAuthHeader() {
  const userToken = getUserToken();
  const userId = getUserId();

  const authHeader = {};

  if (userToken) {
    authHeader['Authorization'] = `Bearer ${userToken}`;
    // authHeader['schema'] = 'Bearer';
    // authHeader['credentials'] = userToken;
  }

  if (userId) {
    authHeader['X-User-Id'] = userId;
  }

  return authHeader;
}

/**
 * 获取用户Token
 */
function getUserToken() {
  // 优先从全局数据获取
  const app = getApp();
  if (app && app.globalData && app.globalData.userToken) {
    return app.globalData.userToken;
  }

  // 从本地存储获取
  return wx.getStorageSync('userToken') || null;
}

/**
 * 获取用户ID
 */
function getUserId() {
  // 优先从全局数据获取
  const app = getApp();
  if (app && app.globalData && app.globalData.userId) {
    return app.globalData.userId;
  }

  // 从本地存储获取
  return wx.getStorageSync('userId') || null;
}

/**
 * 获取错误信息
 */
function getErrorMessage(err) {
  if (err.errMsg) {
    if (err.errMsg.includes('timeout')) {
      return '请求超时，请检查网络';
    } else if (err.errMsg.includes('fail')) {
      return '网络错误，请重试';
    }
  }
  return '请求失败，请重试';
}

/**
 * 检查登录状态
 */
function checkLoginStatus() {
  const userToken = getUserToken();
  const userId = getUserId();
  return !!(userToken && userId);
}

/**
 * 处理登录失效
 */
function handleLoginExpired() {
  console.log('登录已失效，清除本地数据');

  // 清除本地存储
  wx.removeStorageSync('userToken');
  wx.removeStorageSync('userId');
  wx.removeStorageSync('membershipInfo');

  // 清除全局数据
  const app = getApp();
  if (app && app.globalData) {
    app.globalData.userToken = null;
    app.globalData.userId = null;
    app.globalData.hasUserInfo = false;
    app.globalData.isMember = false;
    app.globalData.membershipExpiry = null;
  }

  // 重新执行自动登录
  const autoLogin = require('../user/autoLogin');
  autoLogin.silentLogin().then((success) => {
    if (!success) {
      console.log('自动登录失败');
    }
  });
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del,
  checkLoginStatus,
  handleLoginExpired,
  getUserToken,
  getUserId
};
