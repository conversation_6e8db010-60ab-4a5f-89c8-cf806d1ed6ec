// pages/user/login/login.js
const apiConfig = require('../../../config/apiConfig');

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    isLoading: false,
    redirectUrl: '', // 登录成功后的重定向URL
  },

  onLoad(options) {
    // 检查是否支持getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      });
    }

    // 获取重定向URL
    if (options.redirect) {
      this.setData({
        redirectUrl: decodeURIComponent(options.redirect)
      });
    }

    // 检查是否已登录
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('userToken');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.setData({
        hasUserInfo: true,
        userInfo: userInfo
      });
      
      // 如果已登录且有重定向URL，则跳转
      if (this.data.redirectUrl) {
        wx.navigateTo({
          url: this.data.redirectUrl
        });
      }
    }
  },

  // 使用getUserProfile获取用户信息
  getUserProfile() {
    this.setData({ isLoading: true });
    
    // 调用微信API获取用户信息
    wx.getUserProfile({
      desc: '用于完善简历信息',
      success: (res) => {
        this.loginWithUserInfo(res.userInfo);
      },
      fail: (err) => {
        console.error('获取用户信息失败:', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
        this.setData({ isLoading: false });
      }
    });
  },

  // 使用用户信息登录
  loginWithUserInfo(userInfo) {
    // 获取登录凭证
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          // 发送code到后端换取openId和token
          wx.request({
            url: apiConfig.loginUrl,
            method: 'POST',
            data: {
              code: loginRes.code,
              userInfo: userInfo
            },
            success: (res) => {
              if (res.data.success) {
                // 保存token和用户信息
                wx.setStorageSync('userToken', res.data.token);
                wx.setStorageSync('userInfo', userInfo);
                wx.setStorageSync('userId', res.data.userId);
                
                // 更新状态
                this.setData({
                  hasUserInfo: true,
                  userInfo: userInfo,
                  isLoading: false
                });
                
                // 记录登录行为
                this.recordUserAction('login');
                
                // 登录成功提示
                wx.showToast({
                  title: '登录成功',
                  icon: 'success'
                });
                
                // 如果有重定向URL，则跳转
                if (this.data.redirectUrl) {
                  setTimeout(() => {
                    wx.navigateTo({
                      url: this.data.redirectUrl
                    });
                  }, 1500);
                } else {
                  // 否则返回上一页
                  setTimeout(() => {
                    wx.navigateBack();
                  }, 1500);
                }
              } else {
                wx.showToast({
                  title: res.data.message || '登录失败',
                  icon: 'none'
                });
                this.setData({ isLoading: false });
              }
            },
            fail: (err) => {
              console.error('登录请求失败:', err);
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
              this.setData({ isLoading: false });
            }
          });
        } else {
          console.error('获取登录凭证失败:', loginRes);
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
          this.setData({ isLoading: false });
        }
      },
      fail: (err) => {
        console.error('wx.login调用失败:', err);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
        this.setData({ isLoading: false });
      }
    });
  },

  // 记录用户行为
  recordUserAction(actionType, actionDetail = {}) {
    const userId = wx.getStorageSync('userId');
    if (!userId) return;
    
    wx.request({
      url: apiConfig.recordActionUrl,
      method: 'POST',
      data: {
        userId: userId,
        actionType: actionType,
        actionDetail: actionDetail,
        timestamp: new Date().getTime()
      },
      fail: (err) => {
        console.error('记录用户行为失败:', err);
      }
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 记录退出登录行为
          this.recordUserAction('logout');
          
          // 清除本地存储的登录信息
          wx.removeStorageSync('userToken');
          wx.removeStorageSync('userInfo');
          wx.removeStorageSync('userId');
          
          // 更新状态
          this.setData({
            hasUserInfo: false,
            userInfo: null
          });
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  }
})
