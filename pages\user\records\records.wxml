<!-- pages/user/records/records.wxml -->
<view class="container">
  <!-- 使用记录列表 -->
  <view class="records-list" wx:if="{{!isEmpty}}">
    <view class="record-item" wx:for="{{actionRecords}}" wx:key="id">
      <view class="record-type">
        <text class="record-type-text">{{item.actionTypeText}}</text>
      </view>
      <view class="record-content">
        <view class="record-detail" wx:if="{{item.actionDetail}}">
          <block wx:if="{{item.actionDetail.pagePath}}">
            <text class="detail-label">页面:</text>
            <text class="detail-value">{{item.actionDetail.pagePath}}</text>
          </block>
          <block wx:if="{{item.actionDetail.resumeId}}">
            <text class="detail-label">简历ID:</text>
            <text class="detail-value">{{item.actionDetail.resumeId}}</text>
          </block>
          <block wx:if="{{item.actionDetail.templateId}}">
            <text class="detail-label">模板:</text>
            <text class="detail-value">{{item.actionDetail.templateId}}</text>
          </block>
          <block wx:if="{{item.actionDetail.exportType}}">
            <text class="detail-label">导出类型:</text>
            <text class="detail-value">{{item.actionDetail.exportType}}</text>
          </block>
        </view>
        <text class="record-time">{{item.formattedTime}}</text>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && !isLoading}}" bindtap="loadMoreRecords">
      <text>加载更多</text>
    </view>
    <view class="no-more" wx:if="{{!hasMore && actionRecords.length > 0}}">
      <text>没有更多记录了</text>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isEmpty && !isLoading}}">
    <image class="empty-icon" src="/pages/index/images/empty.png" mode="aspectFit"></image>
    <text class="empty-text">暂无使用记录</text>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
