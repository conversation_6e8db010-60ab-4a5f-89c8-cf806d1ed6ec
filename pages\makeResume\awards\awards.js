Page({
  data: {
    awardsList: ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '']  // 16个空字符串
  },

  onLoad() {
    // 加载已保存的数据
    const awardsList = wx.getStorageSync('awardsList') || [];
    if (awardsList.length > 0) {
      // 将已有数据填充到16个空位中
      const newAwardsList = [...this.data.awardsList];
      awardsList.forEach((award, index) => {
        if (index < 16) {
          newAwardsList[index] = award;
        }
      });

      this.setData({
        awardsList: newAwardsList
      });
    }
  },

  // 输入框内容变化时触发
  handleInput(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;

    const awardsList = [...this.data.awardsList];
    awardsList[index] = value;

    this.setData({
      awardsList: awardsList
    });
  },

  // 保存信息
  saveInfo() {
    // 过滤掉空字符串，并确保每个项都是字符串
    const awardsList = this.data.awardsList
      .map(item => {
        if (typeof item === 'object' && item !== null) {
          return item.content || '';
        }
        return item;
      })
      .filter(item => typeof item === 'string' && item.trim() !== '');

    console.log('========== 奖项证书保存开始 ==========');
    console.log('保存的数据:', awardsList);

    // 直接保存到本地存储
    wx.setStorageSync('awardsList', awardsList);

    // 同时保存到resumeManager
    const resumeManager = require('../../../utils/resume/resumeManager.js');
    const currentResumeData = resumeManager.getCurrentResumeData() || {};

    // 更新奖项证书
    currentResumeData.awards = awardsList;

    // 保存到resumeManager
    const success = resumeManager.saveCurrentResumeData(currentResumeData);
    console.log('奖项证书保存到resumeManager结果:', success);

    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500,
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 删除信息
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除所有奖项证书吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            awardsList: ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '']
          });

          wx.setStorageSync('awardsList', []);

          wx.showToast({
            title: '已删除',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            }
          });
        }
      }
    });
  }
});