Page({
  data: {
    internshipEditFormData: {
      startDate: '',
      endDate: '',
      company: '',
      position: '',
      content: ''
    },
    isEdit: false,
    editIndex: -1
  },

  onLoad(options) {
    if (options.index) {
      const index = parseInt(options.index);
      const internshipList = wx.getStorageSync('internshipList') || [];
      const internshipEditFormData = internshipList[index];
      
      this.setData({
        internshipEditFormData,
        isEdit: true,
        editIndex: index
      });
    }
  },

  // 处理输入
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`internshipEditFormData.${field}`]: e.detail.value
    });
  },

  // 处理日期选择
  handleDateChange(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`internshipEditFormData.${field}`]: e.detail.value
    });
  },

  // 设置结束日期为"至今"
  setEndDateToNow() {
    this.setData({
      'internshipEditFormData.endDate': '至今'
    });
  },

  // 保存信息
  saveInfo() {
    const { internshipEditFormData, isEdit, editIndex } = this.data;
    
    // 表单验证
    if (!internshipEditFormData.company) {
      wx.showToast({
        title: '请输入公司名称',
        icon: 'none'
      });
      return;
    }
    if (!internshipEditFormData.position) {
      wx.showToast({
        title: '请输入工作职位',
        icon: 'none'
      });
      return;
    }
    if (!internshipEditFormData.startDate) {
      wx.showToast({
        title: '请选择开始时间',
        icon: 'none'
      });
      return;
    }
    if (!internshipEditFormData.endDate) {
      wx.showToast({
        title: '请选择结束时间',
        icon: 'none'
      });
      return;
    }

    let internshipList = wx.getStorageSync('internshipList') || [];
    
    if (isEdit) {
      internshipList[editIndex] = internshipEditFormData;
    } else {
      internshipList.push(internshipEditFormData);
    }
    
    wx.setStorageSync('internshipList', internshipList);
    
    wx.showToast({
      title: '保存成功',
      icon: 'success',
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 删除
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除该实习经历吗？',
      success: (res) => {
        if (res.confirm) {
          const { editIndex } = this.data;
          let internshipList = wx.getStorageSync('internshipList') || [];
          internshipList.splice(editIndex, 1);
          wx.setStorageSync('internshipList', internshipList);
          
          wx.navigateBack({
            success: () => {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  }
}); 