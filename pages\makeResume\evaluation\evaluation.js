Page({
  data: {
    evaluationFormData: {
      content: ''
    }
  },

  onLoad() {
    // 从本地存储获取数据
    const evaluationList = wx.getStorageSync('evaluationList') || [];
    this.setData({
      evaluationFormData: evaluationList.length > 0 ? evaluationList[0] : { content: '' }
    });
  },

  // 处理输入
  handleInput(e) {
    const content = e.detail.value;
    this.setData({
      'evaluationFormData.content': content
    });

    // 获取当前的moduleOrder
    const moduleOrders = wx.getStorageSync('moduleOrders') || {};
    const moduleOrder = moduleOrders['evaluation'] || 10;

    // 保存到本地存储，包含moduleOrder
    wx.setStorageSync('evaluationList', [{
      content: content,
      moduleOrder: moduleOrder
    }]);
  },

  // 保存评价
  saveEvaluation() {
    const { evaluationFormData } = this.data;

    if (!evaluationFormData.content.trim()) {
      wx.showToast({
        title: '请填写自我评价',
        icon: 'none'
      });
      return;
    }

    // 获取当前的moduleOrder
    const moduleOrders = wx.getStorageSync('moduleOrders') || {};
    const moduleOrder = moduleOrders['evaluation'] || 10;

    console.log('========== 自我评价保存开始 ==========');
    console.log('保存的数据:', evaluationFormData);

    // 保存到本地存储，包含moduleOrder
    const evaluationData = [{
      ...evaluationFormData,
      moduleOrder: moduleOrder
    }];
    wx.setStorageSync('evaluationList', evaluationData);

    // 同时保存到resumeManager
    const resumeManager = require('../../../utils/resume/resumeManager.js');
    const currentResumeData = resumeManager.getCurrentResumeData() || {};

    // 更新自我评价
    currentResumeData.evaluation = evaluationData;

    // 保存到resumeManager
    const success = resumeManager.saveCurrentResumeData(currentResumeData);
    console.log('自我评价保存到resumeManager结果:', success);

    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500,
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  deleteEvaluation() {
    wx.showModal({
      title: '提示',
      content: '确定要删除自我评价吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            'evaluationFormData.content': ''
          });
          // 更新本地存储为空数组
          wx.setStorageSync('evaluationList', []);
          wx.showToast({
            title: '删除成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            }
          });
        }
      }
    });
  }
});