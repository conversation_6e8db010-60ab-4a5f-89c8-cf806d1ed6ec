Page({
  data: {
    evaluationFormData: {
      content: ''
    }
  },

  onLoad() {
    // 从本地存储获取数据
    const evaluationList = wx.getStorageSync('evaluationList') || [];
    this.setData({
      evaluationFormData: evaluationList.length > 0 ? evaluationList[0] : { content: '' }
    });
  },

  // 处理输入
  handleInput(e) {
    const content = e.detail.value;
    this.setData({
      'evaluationFormData.content': content
    });

    // 获取当前的moduleOrder
    const moduleOrders = wx.getStorageSync('moduleOrders') || {};
    const moduleOrder = moduleOrders['evaluation'] || 10;

    // 保存到本地存储，包含moduleOrder
    wx.setStorageSync('evaluationList', [{
      content: content,
      moduleOrder: moduleOrder
    }]);
  },

  // 保存评价
  saveEvaluation() {
    const { evaluationFormData } = this.data;
    
    if (!evaluationFormData.content.trim()) {
      wx.showToast({
        title: '请填写自我评价',
        icon: 'none'
      });
      return;
    }

    // 获取当前的moduleOrder
    const moduleOrders = wx.getStorageSync('moduleOrders') || {};
    const moduleOrder = moduleOrders['evaluation'] || 10;

    // 保存到本地存储，包含moduleOrder
    wx.setStorageSync('evaluationList', [{
      ...evaluationFormData,
      moduleOrder: moduleOrder
    }]);

    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500,
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  deleteEvaluation() {
    wx.showModal({
      title: '提示',
      content: '确定要删除自我评价吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            'evaluationFormData.content': ''
          });
          // 更新本地存储为空数组
          wx.setStorageSync('evaluationList', []);
          wx.showToast({
            title: '删除成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            }
          });
        }
      }
    });
  }
}); 