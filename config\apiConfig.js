// 服务器 URL 配置
const apiConfig = {
    dev: {
        // 基础URL
        baseUrl: 'http://gbw8848.cn',

        // 简历相关API
        renderTemplateUrl: 'http://gbw8848.cn/resume/renderTemplateA01', // 开发环境
        generatePDFUrl: 'http://gbw8848.cn/resume/export-pdf',
        exportJpegUrl: 'http://gbw8848.cn/resume/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl: 'http://gbw8848.cn/user/login', // 用户登录
        userInfoUrl: 'http://gbw8848.cn/user/info', // 获取用户信息
        updateUserInfoUrl: 'http://gbw8848.cn/user/update', // 更新用户信息

        // 用户行为记录API
        recordActionUrl: 'http://gbw8848.cn/user/action/record', // 记录单个行为
        batchRecordActionUrl: 'http://gbw8848.cn/user/action/batch', // 批量记录行为
        userStatsUrl: 'http://gbw8848.cn/user/stats', // 获取用户统计数据
        recentActionsUrl: 'http://gbw8848.cn/user/action/recent', // 获取最近操作记录

        // 简历云存储API
        saveResumeUrl: 'http://gbw8848.cn/resume/save', // 保存简历到云端
        listResumesUrl: 'http://gbw8848.cn/resume/list', // 获取用户简历列表
        getResumeUrl: 'http://gbw8848.cn/resume/get', // 获取指定简历
        deleteResumeUrl: 'http://gbw8848.cn/resume/delete', // 删除简历

        // 反馈API
        feedbackUrl: 'http://gbw8848.cn/feedback/submit' // 提交用户反馈
    },
    test: {
        // 基础URL
        baseUrl: 'http:192.168.1.218:18080',

        // 简历相关API
        renderTemplateUrl: 'http://192.168.1.218:18080/resume/renderTemplateA01', // 测试环境
        generatePDFUrl: 'http://192.168.1.218:18080/resume/export-pdf',
        exportJpegUrl: 'http://192.168.1.218:18080/resume/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl: 'http://192.168.1.218:18080/auth/login', // 用户登录
        userInfoUrl: 'http://192.168.1.218:18080/auth/user', // 获取用户信息
        updateUserInfoUrl: 'http://192.168.1.218:18080/auth/user', // 更新用户信息

        // 用户行为记录API
        recordActionUrl: 'http://192.168.1.218:18080/user/action/record', // 记录单个行为
        batchRecordActionUrl: 'http://192.168.1.218:18080/user/action/batch', // 批量记录行为
        userStatsUrl: 'http://192.168.1.218:18080/user/stats', // 获取用户统计数据
        recentActionsUrl: 'http://192.168.1.218:18080/user/action/recent', // 获取最近操作记录

        // 简历云存储API
        saveResumeUrl: 'http://192.168.1.218:18080/resume/save', // 保存简历到云端
        listResumesUrl: 'http://192.168.1.218:18080/resume/list', // 获取用户简历列表
        getResumeUrl: 'http://192.168.1.218:18080/resume/get', // 获取指定简历
        deleteResumeUrl: 'http://192.168.1.218:18080/resume/delete', // 删除简历

        // 反馈API
        feedbackUrl: 'http://192.168.1.218:18080/feedback/submit' // 提交用户反馈
    },
    prod: {
        // 简历相关API
        renderTemplateUrl: 'https://gbw8848.cn/resume/renderTemplateA01', // 生产环境
        generatePDFUrl: 'https://gbw8848.cn/resume/export-pdf',
        exportJpegUrl: 'https://gbw8848.cn/resume/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl: 'https://gbw8848.cn/user/login', // 用户登录
        userInfoUrl: 'https://gbw8848.cn/user/info', // 获取用户信息
        updateUserInfoUrl: 'https://gbw8848.cn/user/update', // 更新用户信息

        // 用户行为记录API
        recordActionUrl: 'https://gbw8848.cn/user/action/record', // 记录单个行为
        batchRecordActionUrl: 'https://gbw8848.cn/user/action/batch', // 批量记录行为
        userStatsUrl: 'https://gbw8848.cn/user/stats', // 获取用户统计数据
        recentActionsUrl: 'https://gbw8848.cn/user/action/recent', // 获取最近操作记录

        // 简历云存储API
        saveResumeUrl: 'https://gbw8848.cn/resume/save', // 保存简历到云端
        listResumesUrl: 'https://gbw8848.cn/resume/list', // 获取用户简历列表
        getResumeUrl: 'https://gbw8848.cn/resume/get', // 获取指定简历
        deleteResumeUrl: 'https://gbw8848.cn/resume/delete', // 删除简历

        // 反馈API
        feedbackUrl: 'https://gbw8848.cn/feedback/submit' // 提交用户反馈
    }
};

// 根据环境导出配置
const env = 'test'; // 默认使用开发环境
module.exports = apiConfig[env];