/**
 * 用户相关API接口
 */
const request = require('./request');

/**
 * 用户登录
 * @param {string} code 微信登录code
 * @param {boolean} autoLogin 是否自动登录
 * @param {boolean} manualLogin 是否手动登录
 */
function login(code, autoLogin = false, manualLogin = false) {
  return request.post('/user/login', {
    code,
    autoLogin,
    manualLogin
  }, {
    needAuth: false, // 登录接口不需要认证
    showLoading: true,
    loadingText: '登录中...'
  });
}

/**
 * 验证token有效性
 */
function validateToken() {
  return request.get('/user/validate', {}, {
    showLoading: false,
    showError: false
  });
}

/**
 * 获取用户信息
 */
function getUserInfo() {
  return request.get('/user/info', {}, {
    showLoading: false
  });
}

/**
 * 获取用户统计数据
 */
function getUserStats() {
  return request.get('/user/stats', {}, {
    showLoading: false
  });
}

/**
 * 获取用户操作记录
 * @param {number} limit 记录数量限制
 */
function getUserRecords(limit = 10) {
  return request.get('/user/records', {
    limit
  }, {
    showLoading: false
  });
}

/**
 * 记录用户行为
 * @param {string} actionType 行为类型
 * @param {Object} actionDetail 行为详情
 */
function recordUserAction(actionType, actionDetail = {}) {
  return request.post('/user/action', {
    actionType,
    actionDetail,
    timestamp: Date.now()
  }, {
    showLoading: false,
    showError: false
  });
}

module.exports = {
  login,
  validateToken,
  getUserInfo,
  getUserStats,
  getUserRecords,
  recordUserAction
};
