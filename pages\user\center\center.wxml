<!-- pages/user/center/center.wxml -->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-header">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl || '/pages/index/images/touXiang.png'}}" mode="aspectFill"></image>
      <view class="user-detail">
        <text class="nickname">{{userInfo.nickName || '微信用户'}}</text>
        <view class="membership-status">
          <text class="status-text {{isMember ? 'member' : 'normal'}}">{{membershipStatus}}</text>
          <button wx:if="{{!isMember}}" class="upgrade-btn" bindtap="upgradeMembership">升级会员</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 使用统计区域 -->
  <view class="stats-section">
    <view class="section-title">使用统计</view>
    <view class="stats-grid">
      <view class="stats-item">
        <text class="stats-value">{{usageStats.resumeCount}}</text>
        <text class="stats-label">简历数量</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{usageStats.previewCount}}</text>
        <text class="stats-label">预览次数</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{usageStats.pdfCount}}</text>
        <text class="stats-label">导出PDF</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单区域 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="goToMyResumes">
      <view class="menu-icon resume-icon"></view>
      <text class="menu-text">我的简历</text>
      <view class="menu-arrow"></view>
    </view>
    <view class="menu-item" bindtap="goToUsageRecords">
      <view class="menu-icon record-icon"></view>
      <text class="menu-text">使用记录</text>
      <view class="menu-arrow"></view>
    </view>
    <view class="menu-item" bindtap="goToSettings">
      <view class="menu-icon settings-icon"></view>
      <text class="menu-text">账号设置</text>
      <view class="menu-arrow"></view>
    </view>
  </view>

  <!-- 最近操作记录区域 -->
  <view class="recent-section" wx:if="{{hasUserInfo && recentActions.length > 0}}">
    <view class="section-title">最近操作</view>
    <view class="recent-list">
      <view class="recent-item" wx:for="{{recentActions}}" wx:key="id">
        <text class="action-type">{{item.actionType}}</text>
        <text class="action-time">{{item.formattedTime}}</text>
      </view>
    </view>
  </view>

  <!-- 退出登录按钮 -->
  <button class="logout-btn" bindtap="logout" wx:if="{{hasUserInfo}}">退出登录</button>
</view>
