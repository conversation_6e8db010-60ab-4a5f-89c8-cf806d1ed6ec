/**
 * 登录拦截器
 * 用于检查用户是否已登录，如果未登录则跳转到登录页面
 */

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
function isLoggedIn() {
  const token = wx.getStorageSync('userToken');
  const userInfo = wx.getStorageSync('userInfo');
  return !!(token && userInfo);
}

/**
 * 检查登录状态，如果未登录则跳转到登录页面
 * @param {string} redirectUrl - 登录成功后的重定向URL，可选
 * @returns {boolean} 是否已登录
 */
function checkLoginStatus(redirectUrl) {
  if (isLoggedIn()) {
    return true;
  }
  
  // 如果未登录，跳转到登录页面
  if (redirectUrl) {
    wx.navigateTo({
      url: `/pages/user/login/login?redirect=${encodeURIComponent(redirectUrl)}`
    });
  } else {
    wx.navigateTo({
      url: '/pages/user/login/login'
    });
  }
  
  return false;
}

/**
 * 获取当前用户ID
 * @returns {string|null} 用户ID，未登录时返回null
 */
function getUserId() {
  if (!isLoggedIn()) {
    return null;
  }
  return wx.getStorageSync('userId');
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息，未登录时返回null
 */
function getUserInfo() {
  if (!isLoggedIn()) {
    return null;
  }
  return wx.getStorageSync('userInfo');
}

/**
 * 获取当前用户Token
 * @returns {string|null} 用户Token，未登录时返回null
 */
function getUserToken() {
  if (!isLoggedIn()) {
    return null;
  }
  return wx.getStorageSync('userToken');
}

/**
 * 清除用户登录信息
 */
function clearLoginInfo() {
  wx.removeStorageSync('userToken');
  wx.removeStorageSync('userInfo');
  wx.removeStorageSync('userId');
}

module.exports = {
  isLoggedIn,
  checkLoginStatus,
  getUserId,
  getUserInfo,
  getUserToken,
  clearLoginInfo
};
