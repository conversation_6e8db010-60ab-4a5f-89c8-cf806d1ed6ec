Page({
  data: {
    workEditFormData: {
      startDate: '',
      endDate: '',
      company: '',
      position: '',
      content: ''
    },
    isEdit: false,
    editIndex: -1
  },

  onLoad(options) {
    if (options.index) {
      const index = parseInt(options.index);
      const workList = wx.getStorageSync('workList') || [];
      const workEditFormData = workList[index];
      
      this.setData({
        workEditFormData,
        isEdit: true,
        editIndex: index
      });
    }
  },

  // 处理输入
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`workEditFormData.${field}`]: e.detail.value
    });
  },

  // 处理日期选择
  handleDateChange(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`workEditFormData.${field}`]: e.detail.value
    });
  },

  // 设置结束日期为"至今"
  setEndDateToNow() {
    this.setData({
      'workEditFormData.endDate': '至今'
    });
  },

  // 保存信息
  saveInfo() {
    const { workEditFormData, isEdit, editIndex } = this.data;
    
    // 表单验证
    if (!workEditFormData.company) {
      wx.showToast({
        title: '请输入公司名称',
        icon: 'none'
      });
      return;
    }
    if (!workEditFormData.position) {
      wx.showToast({
        title: '请输入工作职位',
        icon: 'none'
      });
      return;
    }
    if (!workEditFormData.startDate) {
      wx.showToast({
        title: '请选择开始时间',
        icon: 'none'
      });
      return;
    }
    if (!workEditFormData.endDate) {
      wx.showToast({
        title: '请选择结束时间',
        icon: 'none'
      });
      return;
    }

    let workList = wx.getStorageSync('workList') || [];
    
    if (isEdit) {
      workList[editIndex] = workEditFormData;
    } else {
      workList.push(workEditFormData);
    }
    
    wx.setStorageSync('workList', workList);
    
    wx.showToast({
      title: '保存成功',
      icon: 'success',
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 删除
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除该工作经历吗？',
      success: (res) => {
        if (res.confirm) {
          const { editIndex } = this.data;
          let workList = wx.getStorageSync('workList') || [];
          workList.splice(editIndex, 1);
          wx.setStorageSync('workList', workList);
          
          wx.navigateBack({
            success: () => {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  }
}); 