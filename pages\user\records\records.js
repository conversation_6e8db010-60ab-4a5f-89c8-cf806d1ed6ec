// pages/user/records/records.js
const apiConfig = require('../../../config/apiConfig');
const app = getApp();

Page({
  data: {
    actionRecords: [],
    isLoading: false,
    isEmpty: false,
    currentPage: 1,
    pageSize: 20,
    hasMore: true,
    // 行为类型映射
    actionTypeMap: {
      'login': '登录',
      'logout': '登出',
      'page_view': '页面访问',
      'resume_create': '创建简历',
      'resume_edit': '编辑简历',
      'resume_preview': '预览简历',
      'resume_export': '导出简历',
      'template_switch': '切换模板',
      'style_change': '修改样式'
    }
  },

  onLoad() {
    // 检查登录状态
    if (!app.checkLogin('/pages/user/records/records')) {
      return;
    }
    
    // 加载使用记录
    this.loadActionRecords();
  },

  onShow() {
    // 每次显示页面时刷新数据
    if (app.globalData.hasUserInfo) {
      this.loadActionRecords();
    }
  },

  // 加载使用记录
  loadActionRecords(append = false) {
    const userId = app.getUserId();
    if (!userId) return;
    
    this.setData({ isLoading: true });
    
    wx.request({
      url: apiConfig.recentActionsUrl,
      method: 'GET',
      data: { 
        userId: userId,
        page: this.data.currentPage,
        pageSize: this.data.pageSize
      },
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('userToken')}`
      },
      success: (res) => {
        if (res.data.success) {
          const newRecords = res.data.actions || [];
          
          // 格式化时间和行为类型
          const formattedRecords = newRecords.map(record => {
            return {
              ...record,
              formattedTime: this.formatTime(record.timestamp),
              actionTypeText: this.data.actionTypeMap[record.actionType] || record.actionType
            };
          });
          
          if (append) {
            // 追加数据
            this.setData({
              actionRecords: [...this.data.actionRecords, ...formattedRecords],
              hasMore: formattedRecords.length === this.data.pageSize,
              isEmpty: this.data.actionRecords.length === 0 && formattedRecords.length === 0
            });
          } else {
            // 重置数据
            this.setData({
              actionRecords: formattedRecords,
              hasMore: formattedRecords.length === this.data.pageSize,
              isEmpty: formattedRecords.length === 0,
              currentPage: 1
            });
          }
          
          // 记录用户行为
          app.trackUserAction('view_usage_records');
        } else {
          console.error('获取使用记录失败:', res.data.message);
          wx.showToast({
            title: '获取使用记录失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求使用记录失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
        wx.stopPullDownRefresh();
      }
    });
  },

  // 加载更多记录
  loadMoreRecords() {
    if (!this.data.hasMore || this.data.isLoading) return;
    
    this.setData({
      currentPage: this.data.currentPage + 1
    }, () => {
      this.loadActionRecords(true);
    });
  },

  // 格式化时间戳
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = this.padZero(date.getMonth() + 1);
    const day = this.padZero(date.getDate());
    const hour = this.padZero(date.getHours());
    const minute = this.padZero(date.getMinutes());
    const second = this.padZero(date.getSeconds());
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  },

  // 数字补零
  padZero(num) {
    return num < 10 ? '0' + num : num;
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({
      currentPage: 1
    }, () => {
      this.loadActionRecords();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    this.loadMoreRecords();
  }
})
