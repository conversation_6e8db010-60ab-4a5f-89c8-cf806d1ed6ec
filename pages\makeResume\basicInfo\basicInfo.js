Page({
  data: {
    basicInfoFormData: {
      name: '',
      gender: '',
      phone: '',
      city: '',
      email: '',
      wechat: '',
      age: '',
      birthday: '',
      marriage: '',
      politics: '',
      nation: '',
      hometown: '',
      height: '',
      weight: '',
      photoUrl: '',
      customTitle1: '',
      customContent1: '',
      customTitle2: '',
      customContent2: ''
    },
    genderArray: ['男', '女'],
    genderIndex: -1,
    marriageArray: ['未婚', '已婚', '离异', '丧偶'],
    marriageIndex: -1,
    politicsArray: ['群众', '共青团员', '中共党员', '民主党派'],
    politicsIndex: -1
  },

  onLoad() {
    // 从basicInfo键加载数据
    const basicInfo = wx.getStorageSync('basicInfo');
    if (basicInfo) {
      // 只设置存在的值
      const newBasicInfo = {};
      Object.keys(this.data.basicInfoFormData).forEach(key => {
        if (basicInfo[key]) {
          newBasicInfo[key] = basicInfo[key];
        }
      });
      
      this.setData({
        basicInfoFormData: {...this.data.basicInfoFormData, ...newBasicInfo}
      });
      
      // 设置选择器的索引，只有当值存在时才设置索引
      const genderIndex = basicInfo.gender ? this.data.genderArray.indexOf(basicInfo.gender) : -1;
      const marriageIndex = basicInfo.marriage ? this.data.marriageArray.indexOf(basicInfo.marriage) : -1;
      const politicsIndex = basicInfo.politics ? this.data.politicsArray.indexOf(basicInfo.politics) : -1;
      
      this.setData({
        genderIndex: genderIndex,
        marriageIndex: marriageIndex,
        politicsIndex: politicsIndex
      });
    }
  },

  // 处理输入框内容变化
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`basicInfoFormData.${field}`]: value
    });
  },

  // 处理性别选择器
  handleGenderPicker(e) {
    const index = e.detail.value;
    this.setData({
      genderIndex: index,
      'basicInfoFormData.gender': this.data.genderArray[index]
    });
  },

  // 处理婚姻状况选择器
  handleMarriagePicker(e) {
    const index = parseInt(e.detail.value);
    if (index >= 0 && index < this.data.marriageArray.length) {
      this.setData({
        marriageIndex: index,
        'basicInfoFormData.marriage': this.data.marriageArray[index]
      });
    }
  },

  // 处理政治面貌选择器
  handlePoliticsPicker(e) {
    const index = parseInt(e.detail.value);
    if (index >= 0 && index < this.data.politicsArray.length) {
      this.setData({
        politicsIndex: index,
        'basicInfoFormData.politics': this.data.politicsArray[index]
      });
    }
  },

  // 处理日期选择器
  handlePicker(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    this.setData({
      [`basicInfoFormData.${field}`]: value
    });
  },

  // 清除字段内容
  clearField(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`basicInfoFormData.${field}`]: ''
    });
  },

  // 清除自定义字段
  clearCustomField(e) {
    const { index } = e.currentTarget.dataset;
    this.setData({
      [`basicInfoFormData.customTitle${index}`]: '',
      [`basicInfoFormData.customContent${index}`]: ''
    });
  },

  // 选择照片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({
          'basicInfoFormData.photoUrl': res.tempFilePaths[0]
        });
      }
    });
  },

  // 保存信息
  saveInfo() {
    wx.setStorageSync('basicInfo', this.data.basicInfoFormData);
    
    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500,
      success: () => {
        // 延迟返回，让用户看到保存成功的提示
        setTimeout(() => {
          wx.navigateBack({
            delta: 1  // 返回上一级页面
          });
        }, 1500);
      }
    });
  },

  // 删除信息
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除所有信息吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            basicInfoFormData: {
              name: '',
              gender: '',
              phone: '',
              city: '',
              email: '',
              wechat: '',
              age: '',
              birthday: '',
              marriage: '',
              politics: '',
              nation: '',
              hometown: '',
              height: '',
              weight: '',
              photoUrl: '',
              customTitle1: '',
              customContent1: '',
              customTitle2: '',
              customContent2: ''
            },
            genderIndex: -1,
            marriageIndex: -1,
            politicsIndex: -1
          });
          
          wx.removeStorageSync('basicInfo');
          
          wx.showToast({
            title: '已删除',
            icon: 'success',
            duration: 1500,
            success: () => {
              // 延迟返回，让用户看到删除成功的提示
              setTimeout(() => {
                // 返回上一页并刷新数据
                const pages = getCurrentPages();
                const prePage = pages[pages.length - 2];
                if (prePage) {
                  prePage.onShow(); // 调用上一页的onShow方法刷新数据
                }
                wx.navigateBack({
                  delta: 1
                });
              }, 1500);
            }
          });
        }
      }
    });
  }
}); 