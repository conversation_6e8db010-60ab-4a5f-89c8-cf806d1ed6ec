<!-- pages/user/login/login.wxml -->
<view class="container">
  <view class="header">
    <image class="logo" src="/pages/index/images/touXiang.png" mode="aspectFit"></image>
    <text class="title">个人简历模板Offer必来</text>
  </view>
  
  <view class="content">
    <block wx:if="{{!hasUserInfo}}">
      <view class="login-tips">
        <text>登录后可以享受更多功能：</text>
        <text>· 云端保存简历数据</text>
        <text>· 跨设备访问简历</text>
        <text>· 更多精美模板</text>
      </view>
      
      <button 
        wx:if="{{canIUseGetUserProfile}}" 
        class="login-btn" 
        bindtap="getUserProfile" 
        loading="{{isLoading}}"
        disabled="{{isLoading}}"
      >
        微信一键登录
      </button>
      <button 
        wx:else 
        class="login-btn" 
        open-type="getUserInfo" 
        bindgetuserinfo="getUserInfo" 
        loading="{{isLoading}}"
        disabled="{{isLoading}}"
      >
        微信一键登录
      </button>
      
      <view class="skip-login">
        <text bindtap="skipLogin">暂不登录，继续使用</text>
      </view>
    </block>
    
    <block wx:else>
      <view class="user-info">
        <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <text class="nickname">{{userInfo.nickName}}</text>
      </view>
      
      <view class="action-list">
        <view class="action-item">
          <text>我的简历</text>
          <text class="arrow">></text>
        </view>
        <view class="action-item">
          <text>使用记录</text>
          <text class="arrow">></text>
        </view>
        <view class="action-item">
          <text>账号设置</text>
          <text class="arrow">></text>
        </view>
      </view>
      
      <button class="logout-btn" bindtap="logout">退出登录</button>
    </block>
  </view>
</view>
