<!-- pages/user/login/login.wxml -->
<view class="container">
  <view class="header">
    <image class="logo" src="/pages/index/images/touXiang.png" mode="aspectFit"></image>
    <text class="title">个人简历模板Offer必来</text>
  </view>

  <view class="content">
    <block wx:if="{{loginStatus === 'checking'}}">
      <view class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">检查登录状态...</text>
      </view>
    </block>

    <block wx:elif="{{loginStatus === 'not_logged'}}">
      <view class="login-tips">
        <text>登录后可以享受更多功能：</text>
        <text>· 云端保存简历数据</text>
        <text>· 跨设备访问简历</text>
        <text>· 更多精美模板</text>
      </view>

      <button
        class="login-btn"
        bindtap="performLogin"
        loading="{{isLoading}}"
        disabled="{{isLoading}}"
      >
        微信登录
      </button>

      <view class="skip-login">
        <text bindtap="skipLogin">暂不登录，继续使用</text>
      </view>
    </block>

    <block wx:else>
      <view class="user-info">
        <image class="avatar" src="/pages/index/images/touXiang.png" mode="aspectFill"></image>
        <text class="nickname">微信用户</text>
        <text class="welcome">登录成功！</text>
      </view>

      <button class="logout-btn" bindtap="logout">退出登录</button>
    </block>
  </view>
</view>
