/**
 * 简历模板渲染系统
 */

// 导入模板
let resumeTemplates;
if (typeof require !== 'undefined') {
  resumeTemplates = require('./templates').resumeTemplates;
} else if (typeof window !== 'undefined') {
  resumeTemplates = window.resumeTemplates;
}

/**
 * 简单的模板引擎，替换模板中的变量
 * @param {string} template - 包含{{变量}}的模板字符串
 * @param {Object} data - 包含变量值的对象
 * @returns {string} - 替换变量后的字符串
 */
function renderTemplate(template, data) {
  return template.replace(/\{\{(\w+)\}\}/g, function(match, key) {
    return data[key] !== undefined ? data[key] : '';
  });
}

/**
 * 渲染简历数据到指定模板
 * @param {Object} userData - 用户简历数据
 * @param {string} templateId - 模板ID，例如'template03'
 * @param {string} containerId - 简历容器的ID (可选)
 * @returns {Object} - 返回渲染后的HTML和CSS
 */
function renderResume(userData, templateId = 'template03', containerId = 'resume-container') {
  // 获取指定的模板
  const template = resumeTemplates[templateId];
  if (!template) {
    throw new Error(`Template "${templateId}" not found`);
  }
  
  // 创建一个深拷贝的数据对象，避免修改原始数据
  const resumeData = JSON.parse(JSON.stringify(userData));
  
  // 处理基本信息
  const name = resumeData.name || '';
  const avatar = resumeData.avatar || 'placeholder-avatar.jpg';
  const phone = resumeData.phone || '';
  const email = resumeData.email || '';
  
  // 处理个人信息
  const personalInfo = resumeData.personalInfo || {};
  const ethnicity = personalInfo.ethnicity || '';
  const hometown = personalInfo.hometown || '';
  const currentLocation = personalInfo.currentLocation || '';
  const jobIntention = personalInfo.jobIntention || '';
  const birthday = personalInfo.birthday || '';
  const height = personalInfo.height || '';
  const educationLevel = personalInfo.educationLevel || '';
  const politicalStatus = personalInfo.politicalStatus || '';
  
  // 处理教育背景
  const education = resumeData.education || {};
  const educationTime = education.time || '';
  const school = education.school || '';
  const major = education.major || '';
  const courses = education.courses || '';
  
  // 处理技能证书
  const skills = resumeData.skills || {};
  const certificates = skills.certificates || '';
  const personalSkills = skills.personalSkills || '';
  const skillDetails = skills.details || '';
  
  // 生成在校经历HTML
  const experiencesHTML = template.renderExperience(resumeData.experiences);
  
  // 生成自我评价HTML
  const evaluationsHTML = template.renderEvaluation(resumeData.evaluations);
  
  // 确定哪些部分应该隐藏
  const hideContact = !phone && !email ? 'hidden' : '';
  const hidePersonalInfo = Object.values(personalInfo).every(val => !val) ? 'hidden' : '';
  const hideEducation = Object.values(education).every(val => !val) ? 'hidden' : '';
  const hideExperience = !resumeData.experiences || resumeData.experiences.length === 0 ? 'hidden' : '';
  const hideSkills = Object.values(skills).every(val => !val) ? 'hidden' : '';
  const hideEvaluation = !resumeData.evaluations || resumeData.evaluations.length === 0 ? 'hidden' : '';
  
  // 准备模板数据
  const templateData = {
    containerId,
    name,
    avatar,
    phone,
    email,
    ethnicity,
    hometown,
    currentLocation,
    jobIntention,
    birthday,
    height,
    educationLevel,
    politicalStatus,
    educationTime,
    school,
    major,
    courses,
    certificates,
    personalSkills,
    skillDetails,
    experiencesHTML,
    evaluationsHTML,
    hideContact,
    hidePersonalInfo,
    hideEducation,
    hideExperience,
    hideSkills,
    hideEvaluation
  };
  
  // 渲染HTML模板
  const html = renderTemplate(template.html, templateData);
  
  // 如果在浏览器环境中，可以直接更新DOM
  if (typeof document !== 'undefined') {
    const container = document.getElementById(containerId);
    if (container) {
      container.outerHTML = html;
      
      // 添加样式
      if (!document.getElementById(`${templateId}-style`)) {
        const styleElement = document.createElement('style');
        styleElement.id = `${templateId}-style`;
        styleElement.textContent = template.css;
        document.head.appendChild(styleElement);
      }
      
      return { success: true };
    }
  }
  
  // 返回渲染后的HTML和CSS
  return {
    html,
    css: template.css
  };
}

/**
 * 生成完整的HTML文档
 * @param {Object} userData - 用户简历数据
 * @param {string} templateId - 模板ID
 * @returns {string} - 完整的HTML文档
 */
function generateFullHTML(userData, templateId = 'template03') {
  const result = renderResume(userData, templateId);
  
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${userData.name || '个人'}的简历</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    ${result.css}
  </style>
</head>
<body>
  ${result.html}
</body>
</html>
  `;
}

// 导出函数，以便在Node.js环境中使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { renderResume, generateFullHTML };
} 