// pages/user/center/center.js
const apiConfig = require('../../../config/apiConfig');

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    isMember: false,
    membershipStatus: '普通用户', // 会员状态文本
    membershipExpiry: null,
    isLoading: false
  },

  onLoad() {
    // 检查登录状态
    this.checkLoginStatus();
  },

  onShow() {
    // 每次显示页面时检查登录状态并刷新数据
    this.checkLoginStatus();
    this.updateMembershipStatus();
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('userToken');
    const userId = wx.getStorageSync('userId');

    if (token && userId) {
      this.setData({
        hasUserInfo: true,
        userInfo: { nickName: '微信用户', avatarUrl: '/pages/index/images/touXiang.png' }
      });
      return true;
    } else {
      this.setData({
        hasUserInfo: false,
        userInfo: null
      });
      return false;
    }
  },

  // 更新会员状态
  updateMembershipStatus() {
    const app = getApp();
    const membershipInfo = wx.getStorageSync('membershipInfo');

    let isMember = false;
    let membershipStatus = '普通用户';
    let membershipExpiry = null;

    if (membershipInfo) {
      isMember = membershipInfo.isMember || false;
      membershipExpiry = membershipInfo.expiry;

      if (isMember) {
        if (membershipExpiry) {
          const expiryDate = new Date(membershipExpiry);
          const now = new Date();
          if (expiryDate > now) {
            membershipStatus = '会员用户';
          } else {
            membershipStatus = '会员已过期';
            isMember = false;
          }
        } else {
          membershipStatus = '永久会员';
        }
      }
    }

    this.setData({
      isMember: isMember,
      membershipStatus: membershipStatus,
      membershipExpiry: membershipExpiry
    });

    // 同步更新全局状态
    if (app) {
      app.globalData.isMember = isMember;
      app.globalData.membershipExpiry = membershipExpiry;
    }
  },



  // 手动触发登录（保留方法但不在界面显示按钮）
  goToLogin() {
    wx.navigateTo({
      url: '/pages/user/login/login'
    });
  },

  // 升级会员
  upgradeMembership() {
    wx.showModal({
      title: '升级会员',
      content: '升级会员可享受更多功能和特权，是否前往升级？',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到会员升级页面或调用支付接口
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 跳转到我的简历页面
  goToMyResumes() {
    if (!this.checkLoginStatus()) {
      this.redirectToLogin('/pages/user/resumes/resumes');
      return;
    }

    wx.navigateTo({
      url: '/pages/user/resumes/resumes'
    });
  },



  // 跳转到账号设置页面
  goToSettings() {
    if (!this.checkLoginStatus()) {
      this.redirectToLogin('/pages/user/settings/settings');
      return;
    }

    wx.navigateTo({
      url: '/pages/user/settings/settings'
    });
  },

  // 重定向到登录页面
  redirectToLogin(redirectUrl) {
    wx.navigateTo({
      url: `/pages/user/login/login?redirect=${encodeURIComponent(redirectUrl)}`
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 记录退出登录行为
          this.recordUserAction('logout');

          // 清除本地存储的登录信息
          wx.removeStorageSync('userToken');
          wx.removeStorageSync('userInfo');
          wx.removeStorageSync('userId');

          // 更新状态
          this.setData({
            hasUserInfo: false,
            userInfo: null
          });

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 记录用户行为
  recordUserAction(actionType, actionDetail = {}) {
    const userId = wx.getStorageSync('userId');
    if (!userId) return;

    wx.request({
      url: apiConfig.recordActionUrl,
      method: 'POST',
      data: {
        userId: userId,
        actionType: actionType,
        actionDetail: actionDetail,
        timestamp: new Date().getTime()
      },
      fail: (err) => {
        console.error('记录用户行为失败:', err);
      }
    });
  }
})
