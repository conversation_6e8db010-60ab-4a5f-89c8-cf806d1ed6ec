// pages/feedback/feedback.js
const apiConfig = require('../../config/apiConfig');

Page({
  data: {
    feedbackType: 'suggestion', // suggestion, bug, other
    feedbackContent: '',
    contactInfo: '',
    isSubmitting: false,
    typeOptions: [
      { value: 'suggestion', label: '功能建议', icon: '💡' },
      { value: 'bug', label: '问题反馈', icon: '🐛' },
      { value: 'praise', label: '表扬鼓励', icon: '👍' },
      { value: 'other', label: '其他', icon: '💬' }
    ]
  },

  onLoad() {
    // 记录页面访问
    const app = getApp();
    if (app && app.trackUserAction) {
      app.trackUserAction('page_view', { page: 'feedback' });
    }
  },

  // 选择反馈类型
  onTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      feedbackType: type
    });
  },

  // 输入反馈内容
  onContentInput(e) {
    this.setData({
      feedbackContent: e.detail.value
    });
  },

  // 输入联系方式
  onContactInput(e) {
    this.setData({
      contactInfo: e.detail.value
    });
  },

  // 提交反馈
  submitFeedback() {
    const { feedbackType, feedbackContent, contactInfo } = this.data;
    
    // 验证输入
    if (!feedbackContent.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return;
    }

    if (feedbackContent.trim().length < 10) {
      wx.showToast({
        title: '反馈内容至少10个字符',
        icon: 'none'
      });
      return;
    }

    this.setData({ isSubmitting: true });

    // 构建反馈数据
    const feedbackData = {
      type: feedbackType,
      content: feedbackContent.trim(),
      contact: contactInfo.trim(),
      timestamp: new Date().getTime(),
      userId: wx.getStorageSync('userId') || 'anonymous',
      deviceInfo: this.getDeviceInfo()
    };

    // 发送到服务器
    wx.request({
      url: apiConfig.feedbackUrl || 'https://gbw8848.cn/feedback/submit',
      method: 'POST',
      data: feedbackData,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${wx.getStorageSync('userToken')}`
      },
      success: (res) => {
        if (res.data && res.data.success) {
          // 记录反馈行为
          const app = getApp();
          if (app && app.trackUserAction) {
            app.trackUserAction('submit_feedback', {
              type: feedbackType,
              contentLength: feedbackContent.length
            });
          }

          wx.showToast({
            title: '反馈提交成功',
            icon: 'success',
            duration: 2000
          });

          // 清空表单
          this.setData({
            feedbackContent: '',
            contactInfo: '',
            feedbackType: 'suggestion'
          });

          // 延迟返回
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        } else {
          wx.showToast({
            title: res.data.message || '提交失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('提交反馈失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isSubmitting: false });
      }
    });
  },

  // 获取设备信息
  getDeviceInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      return {
        platform: systemInfo.platform,
        system: systemInfo.system,
        version: systemInfo.version,
        model: systemInfo.model,
        brand: systemInfo.brand,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight
      };
    } catch (error) {
      return {};
    }
  },

  // 自定义分享
  onCustomShare() {
    return {
      title: '个人简历模板制作工具',
      path: '/pages/index/index',
      imageUrl: '/pages/index/images/share-logo.png'
    };
  },

  // 分享给朋友
  onShareAppMessage() {
    return this.onCustomShare();
  },

  // 分享到朋友圈
  onShareTimeline() {
    return this.onCustomShare();
  }
});
