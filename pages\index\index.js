// index.js
const membershipManager = require('../../utils/user/membershipManager');

Page({
  data: {
    title: "欢迎使用小程序    haha",
    description: "这是一个测试页面  page",
  },

  onLoad() {
    console.log('主页加载，查询会员状态...');
    // 在主页加载时查询会员状态
    membershipManager.queryMemberStatus(false)
      .then((membershipInfo) => {
        console.log('主页会员状态查询完成:', membershipInfo);
      })
      .catch((err) => {
        console.error('主页会员状态查询失败:', err);
      });
  },

  // 跳转到制作简历页面
  toMakeResume() {
    wx.navigateTo({
      url: '/pages/makeResume/makeResume'
    })
  },

  // 跳转到简历样式页面
  toResumeStyle() {
    wx.navigateTo({
      url: '/pages/resumeStyle/resumeStyle'
    })
  },

  // 跳转到证件照制作页面
  toIDPhoto() {
    wx.navigateTo({
      url: '/pages/idPhoto/idPhoto'
    })
  },

  // 跳转到免费模板页面
  toWordTemplate() {
    wx.navigateTo({
      url: '/pages/freeResume/index'
    })
  }
})
