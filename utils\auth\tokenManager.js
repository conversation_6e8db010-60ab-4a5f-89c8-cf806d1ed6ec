/**
 * 精简的Token管理工具
 * 只提供token定时刷新功能
 */

const userApi = require('../api/userApi');

// 定时器ID
let refreshTimer = null;

/**
 * 获取当前token信息
 * @returns {Object} token信息
 */
function getTokenInfo() {
  try {
    const tokenInfo = wx.getStorageSync('tokenInfo');
    return tokenInfo || null;
  } catch (error) {
    console.error('获取token信息失败:', error);
    return null;
  }
}

/**
 * 保存token信息并启动定时刷新
 * @param {Object} tokenData 服务端返回的token数据
 */
function saveTokenInfo(tokenData) {
  try {
    const tokenInfo = {
      access_token: tokenData.access_token,
      expires_in: tokenData.expires_in || 1800, // 默认30分钟
      token_type: tokenData.token_type || 'bearer',
      created_at: Date.now(),
      refresh_token: tokenData.refresh_token
    };

    wx.setStorageSync('tokenInfo', tokenInfo);

    // 同步到全局状态
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.userToken = tokenInfo.access_token;
    }

    console.log('Token信息已保存，启动定时刷新');

    // 启动定时刷新
    startTokenRefresh(tokenInfo.expires_in);

    return tokenInfo;
  } catch (error) {
    console.error('保存token信息失败:', error);
    throw error;
  }
}

/**
 * 启动定时刷新token
 * @param {number} expiresIn token有效期（秒）
 */
function startTokenRefresh(expiresIn) {
  // 清除之前的定时器
  if (refreshTimer) {
    clearTimeout(refreshTimer);
  }

  // 在token过期前5分钟刷新
  const refreshTime = Math.max((expiresIn - 300) * 1000, 60000); // 最少1分钟后刷新

  console.log(`将在${Math.floor(refreshTime / 1000)}秒后刷新token`);

  refreshTimer = setTimeout(async () => {
    console.log('定时刷新token...');
    try {
      await refreshToken();
    } catch (error) {
      console.error('定时刷新token失败:', error);
      // 刷新失败，5分钟后重试
      setTimeout(() => startTokenRefresh(300), 5 * 60 * 1000);
    }
  }, refreshTime);
}

/**
 * 停止定时刷新
 */
function stopTokenRefresh() {
  if (refreshTimer) {
    clearTimeout(refreshTimer);
    refreshTimer = null;
    console.log('已停止token定时刷新');
  }
}

/**
 * 刷新token
 * @returns {Promise<string>} 新的access_token
 */
async function refreshToken() {
  try {
    console.log('开始刷新token...');

    const tokenInfo = getTokenInfo();
    if (!tokenInfo || !tokenInfo.refresh_token) {
      throw new Error('没有refresh_token，需要重新登录');
    }

    // 调用刷新接口
    const response = await userApi.refreshToken();

    // 保存新的token信息（会自动启动新的定时刷新）
    const newTokenInfo = saveTokenInfo(response);

    console.log('Token刷新成功');

    return newTokenInfo.access_token;

  } catch (error) {
    console.error('Token刷新失败:', error);
    throw error;
  }
}

/**
 * 自动重新登录
 * @returns {Promise<string>} 新的access_token
 */
async function autoReLogin() {
  console.log('尝试自动重新登录...');

  return new Promise((resolve, reject) => {
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          userApi.login(loginRes.code)
            .then((response) => {
              console.log('自动重新登录成功');

              // 保存新的token信息
              const tokenInfo = saveTokenInfo(response);

              // 更新用户信息
              const app = getApp();
              if (app && response.user_info) {
                app.globalData.userId = response.user_info.id;
                app.globalData.userInfo = response.user_info;
                app.globalData.hasUserInfo = true;

                wx.setStorageSync('userId', response.user_info.id);
                wx.setStorageSync('userInfo', response.user_info);
              }

              resolve(tokenInfo.access_token);
            })
            .catch(reject);
        } else {
          reject(new Error('获取微信登录code失败'));
        }
      },
      fail: reject
    });
  });
}

/**
 * 清除token信息并停止定时刷新
 */
function clearTokenInfo() {
  try {
    wx.removeStorageSync('tokenInfo');

    // 停止定时刷新
    stopTokenRefresh();

    // 清除全局状态
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.userToken = null;
    }

    console.log('Token信息已清除');
  } catch (error) {
    console.error('清除token信息失败:', error);
  }
}

module.exports = {
  getTokenInfo,
  saveTokenInfo,
  refreshToken,
  autoReLogin,
  clearTokenInfo,
  stopTokenRefresh
};
