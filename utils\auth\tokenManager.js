/**
 * Token管理工具
 * 提供token的存储、刷新、验证和自动管理功能
 */

const userApi = require('../api/userApi');

// Token状态常量
const TOKEN_STATUS = {
  VALID: 'valid',           // 有效
  EXPIRED: 'expired',       // 已过期
  REFRESHING: 'refreshing', // 刷新中
  INVALID: 'invalid'        // 无效
};

// 全局状态
let isRefreshing = false;           // 是否正在刷新token
let refreshPromise = null;          // 刷新Promise，避免重复刷新
let failedRequestsQueue = [];       // 失败请求队列

/**
 * 获取当前token信息
 * @returns {Object} token信息
 */
function getTokenInfo() {
  try {
    const tokenInfo = wx.getStorageSync('tokenInfo');
    if (!tokenInfo) {
      return null;
    }
    
    return {
      access_token: tokenInfo.access_token,
      expires_in: tokenInfo.expires_in,
      token_type: tokenInfo.token_type || 'bearer',
      created_at: tokenInfo.created_at,
      refresh_token: tokenInfo.refresh_token
    };
  } catch (error) {
    console.error('获取token信息失败:', error);
    return null;
  }
}

/**
 * 保存token信息
 * @param {Object} tokenData 服务端返回的token数据
 */
function saveTokenInfo(tokenData) {
  try {
    const tokenInfo = {
      access_token: tokenData.access_token,
      expires_in: tokenData.expires_in || 1800, // 默认30分钟
      token_type: tokenData.token_type || 'bearer',
      created_at: Date.now(),
      refresh_token: tokenData.refresh_token
    };
    
    wx.setStorageSync('tokenInfo', tokenInfo);
    
    // 同步到全局状态
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.userToken = tokenInfo.access_token;
    }
    
    console.log('Token信息已保存:', {
      expires_in: tokenInfo.expires_in,
      created_at: new Date(tokenInfo.created_at).toLocaleString()
    });
    
    return tokenInfo;
  } catch (error) {
    console.error('保存token信息失败:', error);
    throw error;
  }
}

/**
 * 检查token是否即将过期
 * @param {number} bufferTime 缓冲时间（秒），默认5分钟
 * @returns {boolean} 是否即将过期
 */
function isTokenExpiringSoon(bufferTime = 300) {
  const tokenInfo = getTokenInfo();
  if (!tokenInfo) {
    return true;
  }
  
  const now = Date.now();
  const expiryTime = tokenInfo.created_at + (tokenInfo.expires_in * 1000);
  const bufferTimeMs = bufferTime * 1000;
  
  return (expiryTime - now) <= bufferTimeMs;
}

/**
 * 检查token状态
 * @returns {string} token状态
 */
function getTokenStatus() {
  const tokenInfo = getTokenInfo();
  
  if (!tokenInfo || !tokenInfo.access_token) {
    return TOKEN_STATUS.INVALID;
  }
  
  if (isRefreshing) {
    return TOKEN_STATUS.REFRESHING;
  }
  
  const now = Date.now();
  const expiryTime = tokenInfo.created_at + (tokenInfo.expires_in * 1000);
  
  if (now >= expiryTime) {
    return TOKEN_STATUS.EXPIRED;
  }
  
  return TOKEN_STATUS.VALID;
}

/**
 * 刷新token
 * @returns {Promise<string>} 新的access_token
 */
function refreshToken() {
  // 如果已经在刷新中，返回现有的Promise
  if (isRefreshing && refreshPromise) {
    return refreshPromise;
  }
  
  isRefreshing = true;
  
  refreshPromise = new Promise(async (resolve, reject) => {
    try {
      console.log('开始刷新token...');
      
      const tokenInfo = getTokenInfo();
      if (!tokenInfo || !tokenInfo.refresh_token) {
        throw new Error('没有refresh_token，需要重新登录');
      }
      
      // 调用刷新接口
      const response = await userApi.refreshToken();
      
      // 保存新的token信息
      const newTokenInfo = saveTokenInfo(response);
      
      console.log('Token刷新成功');
      
      // 处理等待队列中的请求
      processFailedRequestsQueue(newTokenInfo.access_token);
      
      resolve(newTokenInfo.access_token);
      
    } catch (error) {
      console.error('Token刷新失败:', error);
      
      // 刷新失败，清除token信息
      clearTokenInfo();
      
      // 处理等待队列中的请求（全部失败）
      processFailedRequestsQueue(null, error);
      
      reject(error);
    } finally {
      isRefreshing = false;
      refreshPromise = null;
    }
  });
  
  return refreshPromise;
}

/**
 * 自动重新登录
 * @returns {Promise<string>} 新的access_token
 */
function autoReLogin() {
  return new Promise((resolve, reject) => {
    console.log('Token刷新失败，尝试自动重新登录...');
    
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          userApi.login(loginRes.code)
            .then((response) => {
              console.log('自动重新登录成功');
              
              // 保存新的token信息
              const tokenInfo = saveTokenInfo(response);
              
              // 更新用户信息
              const app = getApp();
              if (app && response.user_info) {
                app.globalData.userId = response.user_info.id;
                app.globalData.userInfo = response.user_info;
                app.globalData.hasUserInfo = true;
                
                wx.setStorageSync('userId', response.user_info.id);
                wx.setStorageSync('userInfo', response.user_info);
              }
              
              resolve(tokenInfo.access_token);
            })
            .catch((error) => {
              console.error('自动重新登录失败:', error);
              reject(error);
            });
        } else {
          reject(new Error('获取微信登录code失败'));
        }
      },
      fail: (error) => {
        console.error('微信登录失败:', error);
        reject(error);
      }
    });
  });
}

/**
 * 获取有效的token
 * @param {boolean} forceRefresh 是否强制刷新
 * @returns {Promise<string>} 有效的access_token
 */
async function getValidToken(forceRefresh = false) {
  const status = getTokenStatus();
  
  // 如果token有效且不强制刷新，直接返回
  if (status === TOKEN_STATUS.VALID && !forceRefresh && !isTokenExpiringSoon()) {
    const tokenInfo = getTokenInfo();
    return tokenInfo.access_token;
  }
  
  // 如果正在刷新中，等待刷新完成
  if (status === TOKEN_STATUS.REFRESHING) {
    return await refreshPromise;
  }
  
  // 如果token过期或即将过期，尝试刷新
  if (status === TOKEN_STATUS.EXPIRED || isTokenExpiringSoon() || forceRefresh) {
    try {
      return await refreshToken();
    } catch (refreshError) {
      // 刷新失败，尝试自动重新登录
      try {
        return await autoReLogin();
      } catch (loginError) {
        // 自动登录也失败，抛出错误
        throw new Error('无法获取有效token，请手动重新登录');
      }
    }
  }
  
  // token无效，需要重新登录
  if (status === TOKEN_STATUS.INVALID) {
    try {
      return await autoReLogin();
    } catch (error) {
      throw new Error('无法获取有效token，请手动重新登录');
    }
  }
  
  throw new Error('未知的token状态');
}

/**
 * 添加失败的请求到队列
 * @param {Function} resolve 成功回调
 * @param {Function} reject 失败回调
 */
function addFailedRequest(resolve, reject) {
  failedRequestsQueue.push({ resolve, reject });
}

/**
 * 处理失败请求队列
 * @param {string} token 新的token
 * @param {Error} error 错误信息
 */
function processFailedRequestsQueue(token, error = null) {
  const queue = failedRequestsQueue.slice();
  failedRequestsQueue = [];
  
  queue.forEach(({ resolve, reject }) => {
    if (token) {
      resolve(token);
    } else {
      reject(error || new Error('Token刷新失败'));
    }
  });
}

/**
 * 清除token信息
 */
function clearTokenInfo() {
  try {
    wx.removeStorageSync('tokenInfo');
    
    // 清除全局状态
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.userToken = null;
    }
    
    console.log('Token信息已清除');
  } catch (error) {
    console.error('清除token信息失败:', error);
  }
}

/**
 * 检查是否需要登录
 * @returns {boolean} 是否需要登录
 */
function needsLogin() {
  const status = getTokenStatus();
  return status === TOKEN_STATUS.INVALID;
}

module.exports = {
  TOKEN_STATUS,
  getTokenInfo,
  saveTokenInfo,
  isTokenExpiringSoon,
  getTokenStatus,
  refreshToken,
  autoReLogin,
  getValidToken,
  addFailedRequest,
  clearTokenInfo,
  needsLogin
};
