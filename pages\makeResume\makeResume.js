const app = getApp();
const resumeManager = require('../../utils/resume/resumeManager');

Page({
  data: {
    modules: [
      { id: 1, type: 'basicInfo', name: '基本信息' },
      { id: 2, type: 'jobIntention', name: '求职意向' },
      { id: 3, type: 'education', name: '教育经历' },
      { id: 4, type: 'school', name: '在校经历' },
      { id: 5, type: 'internship', name: '实习经历' },
      { id: 6, type: 'work', name: '工作经历' },
      { id: 7, type: 'project', name: '项目经历' },
      { id: 8, type: 'skills', name: '技能特长' },
      { id: 9, type: 'awards', name: '奖项证书' },
      { id: 10, type: 'interests', name: '兴趣爱好' },
      { id: 11, type: 'evaluation', name: '自我评价' },
      { id: 12, type: 'custom1', name: '自定义名称一' },
      { id: 13, type: 'custom2', name: '自定义名称二' },
      { id: 14, type: 'custom3', name: '自定义名称三' }
    ],
    availableModulesToAdd: [], // 用于"添加更多模块"区域显示
    activeModules: [],
    moduleOrders: {},

    // 当前简历信息
    currentResumeId: null,
    currentResumeTitle: '',

    basicInfo: {},
    jobIntention: {},
    education: [],
    school: [],
    internship: [],
    work: [],
    project: [],
    skills: [],
    awards: [],
    interests: [],
    evaluation: [],
    custom1: [],
    custom2: [],
    custom3: []

  },

  // 添加onLoad生命周期函数
  onLoad() {
    console.log('========== makeResume页面onLoad ==========');
    // 页面加载逻辑
  },

  // 下拉刷新
  onPullDownRefresh() {
    // 重新加载数据
    this.onShow();

    // 停止下拉刷新动画
    wx.stopPullDownRefresh();
  },

  // 页面显示时加载数据
  onShow() {
    console.log('========== makeResume onShow 开始 ==========');

    // 获取当前简历ID
    const currentResumeId = resumeManager.getCurrentResumeId();
    console.log('当前简历ID:', currentResumeId);

    if (!currentResumeId) {
      // 如果没有当前简历，创建一个默认简历
      console.log('没有当前简历，创建默认简历...');
      const resumeInfo = resumeManager.createResume('我的简历');
      if (resumeInfo) {
        resumeManager.setCurrentResumeId(resumeInfo.id);
        this.setData({
          currentResumeId: resumeInfo.id,
          currentResumeTitle: resumeInfo.title
        });
        console.log('创建默认简历成功:', resumeInfo);
      }
    } else {
      // 获取当前简历信息
      const resumesList = resumeManager.getResumesList();
      const currentResume = resumesList.find(resume => resume.id === currentResumeId);
      console.log('找到当前简历:', currentResume);

      this.setData({
        currentResumeId: currentResumeId,
        currentResumeTitle: currentResume ? currentResume.title : '未知简历'
      });
    }

    // 从当前简历加载数据
    console.log('开始加载当前简历数据...');
    this.loadCurrentResumeData();

    // 过滤掉已经有数据的模块，不在添加更多模块区域显示
    console.log('开始过滤模块...');
    this.filterAvailableModulesToAdd();

    console.log('========== makeResume onShow 结束 ==========');
  },

  // 加载当前简历数据
  loadCurrentResumeData() {
    console.log('========== loadCurrentResumeData 开始 ==========');
    const currentResumeData = resumeManager.getCurrentResumeData();
    console.log('从resumeManager获取的数据:', currentResumeData);

    if (currentResumeData) {
      console.log('========== 有简历数据，开始设置 ==========');
      console.log('basicInfo:', currentResumeData.basicInfo);
      console.log('educations:', currentResumeData.educations);
      console.log('skills:', currentResumeData.skills);

      this.setData({
        basicInfo: currentResumeData.basicInfo || {},
        jobIntention: currentResumeData.jobIntention || {},
        education: currentResumeData.educations || [],
        school: currentResumeData.schools || [],
        internship: currentResumeData.internships || [],
        work: currentResumeData.works || [],
        project: currentResumeData.projects || [],
        skills: currentResumeData.skills || [],
        awards: currentResumeData.awards || [],
        interests: currentResumeData.interests || [],
        evaluation: currentResumeData.evaluation || [],
        custom1: currentResumeData.customs || [],
        custom2: [],
        custom3: []
      }, () => {
        console.log('========== setData完成，开始过滤模块 ==========');
        console.log('设置后的basicInfo:', this.data.basicInfo);
        console.log('设置后的education:', this.data.education);
        console.log('设置后的skills:', this.data.skills);
        // 数据设置完成后，立即更新activeModules和availableModulesToAdd
        this.filterAvailableModulesToAdd();
      });
    } else {
      console.log('========== 没有简历数据，使用空数据 ==========');
      // 如果没有数据，使用空数据
      this.setData({
        basicInfo: {},
        jobIntention: {},
        education: [],
        school: [],
        internship: [],
        work: [],
        project: [],
        skills: [],
        awards: [],
        interests: [],
        evaluation: [],
        custom1: [],
        custom2: [],
        custom3: []
      }, () => {
        console.log('========== 空数据setData完成，开始过滤模块 ==========');
        // 数据设置完成后，立即更新activeModules和availableModulesToAdd
        this.filterAvailableModulesToAdd();
      });
    }
    console.log('========== loadCurrentResumeData 结束 ==========');
  },

  // 保存当前简历数据
  saveCurrentResumeData() {
    const resumeData = {
      basicInfo: this.data.basicInfo,
      jobIntention: this.data.jobIntention,
      educations: this.data.education,
      schools: this.data.school,
      internships: this.data.internship,
      works: this.data.work,
      projects: this.data.project,
      skills: this.data.skills,
      awards: this.data.awards,
      interests: this.data.interests,
      evaluation: this.data.evaluation,
      customs: this.data.custom1
    };

    const success = resumeManager.saveCurrentResumeData(resumeData);
    if (success) {
      console.log('简历数据保存成功');
    } else {
      console.error('简历数据保存失败');
    }
  },



  // 过滤出可以添加的模块
  filterAvailableModulesToAdd() {
    console.log('========== filterAvailableModulesToAdd 开始 ==========');
    const allModules = this.data.modules;
    console.log('所有模块:', allModules);

    let moduleOrders = wx.getStorageSync('moduleOrders') || {};
    console.log('模块顺序:', moduleOrders);

    const {
      basicInfo, jobIntention, education, school, internship,
      work, project, skills, awards, interests,
      evaluation, custom1, custom2, custom3
    } = this.data;

    console.log('========== 检查各模块数据 ==========');
    console.log('basicInfo:', basicInfo);
    console.log('jobIntention:', jobIntention);
    console.log('education:', education);
    console.log('school:', school);
    console.log('internship:', internship);
    console.log('work:', work);
    console.log('project:', project);
    console.log('skills:', skills);
    console.log('awards:', awards);
    console.log('interests:', interests);
    console.log('evaluation:', evaluation);
    console.log('custom1:', custom1);

    // 创建一个已有内容的模块类型数组
    const filledModules = [];

    console.log('========== 开始检查模块内容 ==========');

    if (basicInfo && basicInfo.name) {
      console.log('基本信息有内容:', basicInfo.name);
      filledModules.push('basicInfo');
    } else {
      console.log('基本信息无内容:', basicInfo);
    }

    if (jobIntention && (jobIntention.position || jobIntention.location || jobIntention.salary || jobIntention.status)) {
      console.log('求职意向有内容');
      filledModules.push('jobIntention');
    } else {
      console.log('求职意向无内容:', jobIntention);
    }

    if (education && education.length > 0) {
      console.log('教育经历有内容:', education.length, '条');
      filledModules.push('education');
    } else {
      console.log('教育经历无内容:', education);
    }

    if (school && school.length > 0) {
      console.log('在校经历有内容:', school.length, '条');
      filledModules.push('school');
    }
    if (internship && internship.length > 0) {
      console.log('实习经历有内容:', internship.length, '条');
      filledModules.push('internship');
    }
    if (work && work.length > 0) {
      console.log('工作经历有内容:', work.length, '条');
      filledModules.push('work');
    }
    if (project && project.length > 0) {
      console.log('项目经历有内容:', project.length, '条');
      filledModules.push('project');
    }
    if (skills && skills.length > 0) {
      console.log('技能特长有内容:', skills.length, '条');
      filledModules.push('skills');
    }
    if (awards && awards.length > 0) {
      console.log('奖项证书有内容:', awards.length, '条');
      filledModules.push('awards');
    }
    if (interests && interests.length > 0) {
      console.log('兴趣爱好有内容:', interests.length, '条');
      filledModules.push('interests');
    }
    if (evaluation && evaluation.length > 0) {
      console.log('自我评价有内容:', evaluation.length, '条');
      filledModules.push('evaluation');
    }

    // 检查自定义模块对象是否存在且名称不为空
    if (custom1 && custom1.length > 0 && custom1.some(item => item.customName || item.content || item.role)) {
      console.log('自定义模块1有内容');
      filledModules.push('custom1');
    }
    if (custom2 && custom2.length > 0 && custom2.some(item => item.customName || item.content || item.role)) {
      console.log('自定义模块2有内容');
      filledModules.push('custom2');
    }
    if (custom3 && custom3.length > 0 && custom3.some(item => item.customName || item.content || item.role)) {
      console.log('自定义模块3有内容');
      filledModules.push('custom3');
    }

    // completeFillModuleOrders
    moduleOrders = this.completeFillModuleOrders(moduleOrders);

    // 过滤，只保留没有内容的模块
    const emptyModules = allModules.filter(module =>
      !filledModules.includes(module.type)
    );

    let activeModules = allModules.filter(module =>
      filledModules.includes(module.type)
    );

    activeModules = activeModules.sort((a, b) => {
      const orderA = moduleOrders[a.type] !== undefined ? moduleOrders[a.type] : Infinity;
      const orderB = moduleOrders[b.type] !== undefined ? moduleOrders[b.type] : Infinity;
      return orderA - orderB;
    });


    console.log('========== 过滤结果 ==========');
    console.log('filledModules:', filledModules);
    console.log('activeModules:', activeModules);
    console.log('activeModules.length:', activeModules.length);
    console.log('emptyModules:', emptyModules);
    console.log('emptyModules.length:', emptyModules.length);
    console.log('moduleOrders:', moduleOrders);

    // 更新data
    this.setData({
      availableModulesToAdd: emptyModules, // 更新到新的 data 属性
      activeModules: activeModules,
      moduleOrders: moduleOrders
    }, () => {
      console.log('========== setData完成后的最终状态 ==========');
      console.log('this.data.activeModules:', this.data.activeModules);
      console.log('this.data.activeModules.length:', this.data.activeModules.length);
      console.log('this.data.availableModulesToAdd:', this.data.availableModulesToAdd);
      console.log('this.data.availableModulesToAdd.length:', this.data.availableModulesToAdd.length);
      console.log('this.data.basicInfo:', this.data.basicInfo);
      console.log('this.data.education:', this.data.education);
      console.log('========== filterAvailableModulesToAdd 结束 ==========');
    });

    // 更新本地存储
    wx.setStorageSync('moduleOrders', moduleOrders);
    wx.setStorageSync('activeModules', activeModules);

  },

  completeFillModuleOrders(moduleOrders) {

    console.log('============= moduleOrders before sort ============================');
    console.log('moduleOrders:', moduleOrders);

    // 更新moduleOrders
    let maxOrder = 0;
    this.data.modules.forEach(module => {
      if (moduleOrders[module.type]) {
        maxOrder = Math.max(maxOrder, moduleOrders[module.type]);
      }
    });
    console.log('maxOrder:', maxOrder);
    // 根据moduleOrders排序

    // 遍历 modules 数组
    this.data.modules.forEach(module => {
        const type = module.type;
        // 如果 moduleOrders 中不存在该类型，则添加并分配新的顺序值
        if (moduleOrders[type] === undefined) {
            maxOrder++;
            moduleOrders[type] = maxOrder;
        }
    });

    console.log('============= moduleOrders after sort ============================');
    console.log('moduleOrders:', moduleOrders);
    return moduleOrders;
  },

  // 添加证件照
  addPhoto() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 处理选中的图片
      }
    })
  },

  // 添加模块
  addModule(e) {
    const moduleType = e.currentTarget.dataset.type;
    // 根据不同的模块类型跳转到不同的页面
    switch (moduleType) {
      case 'basicInfo':
        wx.navigateTo({
          url: '/pages/makeResume/basicInfo/basicInfo'
        });
        break;
      case 'jobIntention':
        wx.navigateTo({
          url: '/pages/makeResume/jobIntention/jobIntention'
        });
        break;

      case 'education':
        wx.navigateTo({
          url: '/pages/makeResume/education/education/education'
        });
        break;
      case 'school':
        wx.navigateTo({
          url: '/pages/makeResume/school/school/school'
        });
        break;
      case 'internship':
        wx.navigateTo({
          url: '/pages/makeResume/internship/internshipExperience/internshipExperience'
        });
        break;
      case 'work':
        wx.navigateTo({
          url: '/pages/makeResume/work/work/work'
        });
        break;
      case 'project':  // 添加项目经历的处理
        wx.navigateTo({
          url: '/pages/makeResume/project/project/project'
        });
        break;
      case 'skills':
        wx.navigateTo({
          url: '/pages/makeResume/skills/skills'
        });
        break;
      case 'awards':  // 添加奖项证书的处理
        wx.navigateTo({
          url: '/pages/makeResume/awards/awards'
        });
        break;
      case 'interests':
        wx.navigateTo({
          url: '/pages/makeResume/interests/interests'
        });
        break;
      case 'evaluation':
        wx.navigateTo({
          url: '/pages/makeResume/evaluation/evaluation'
        });
        break;
      // 添加自定义模块的跳转逻辑
      case 'custom1':
        wx.navigateTo({
          url: '/pages/makeResume/custom/custom1/custom1'
        });
        break;
      case 'custom2':
        wx.navigateTo({
          url: '/pages/makeResume/custom/custom2/custom2'
        });
        break;
      case 'custom3':
        wx.navigateTo({
          url: '/pages/makeResume/custom/custom3/custom3'
        });
        break;
      // 其他模块的处理可以继续添加
      default:
        console.warn('未知的添加模块类型:', moduleType);
        break;
    }
  },

  // 模块管理
  handleModules() {
    wx.navigateTo({
      url: '/pages/makeResume/moduleManage/moduleManage'
    });
  },

  // AI智能优化
  handleAI() {
    wx.showToast({
      title: '该功能开发中......',
      icon: 'none',
      duration: 2000
    });
  },

  // 生成简历
  generateResume() {
    // 先保存当前简历数据
    this.saveCurrentResumeData();

    let moduleOrders = wx.getStorageSync('moduleOrders');
    if (!moduleOrders) {
      moduleOrders = {};
      this.data.activeModules.forEach((module, index) => {
        moduleOrders[module.type] = index;
      });
      wx.setStorageSync('moduleOrders', moduleOrders);
    }

    const resumeData = {
      moduleOrders: this.data.moduleOrders,
      basicInfo: this.data.basicInfo,
      jobIntention: this.data.jobIntention,
      education: this.data.education,
      school: this.data.school,
      internship: this.data.internship,
      work: this.data.work,
      project: this.data.project,
      skills: this.data.skills,
      awards: this.data.awards,
      interests: this.data.interests,
      evaluation: this.data.evaluation,
      custom1: this.data.custom1,
      custom2: this.data.custom2,
      custom3: this.data.custom3
    }

    console.log('========== makeResume传出的数据 ==========');
    console.log('当前简历ID:', this.data.currentResumeId);
    console.log('当前简历标题:', this.data.currentResumeTitle);
    console.log('基本信息：', resumeData.basicInfo);
    console.log('求职意向：', resumeData.jobIntention);
    console.log('教育经历：', resumeData.education);
    console.log('在校经历：', resumeData.school);
    console.log('实习经历：', resumeData.internship);
    console.log('工作经历：', resumeData.work);
    console.log('项目经历：', resumeData.project);
    console.log('技能特长：', resumeData.skills);
    console.log('获奖证书：', resumeData.awards);
    console.log('兴趣爱好：', resumeData.interests);
    console.log('自我评价：', resumeData.evaluation);
    console.log('自定义模块：', resumeData.custom1, resumeData.custom2, resumeData.custom3);
    console.log('==========================================');

    // 将数据转换为查询字符串
    console.log('resumeData:', resumeData);
    const queryString = encodeURIComponent(JSON.stringify(resumeData));

    // 记录用户行为
    if (app && app.trackUserAction) {
      app.trackUserAction('resume_preview_request', {
        resumeId: this.data.currentResumeId,
        resumeTitle: this.data.currentResumeTitle
      });
    }

    // 跳转并传递数据
    wx.navigateTo({
      url: `/pages/makeCreateResume/makeCreateResume?resumeData=${queryString}`,
      success: () => {
        console.log('跳转到预览页面成功');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 选择图片
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 获取图片路径
        const tempFilePath = res.tempFiles[0].tempFilePath;

        // 使用 wx.cropImage 图片裁剪
        wx.cropImage({
          src: tempFilePath,
          cropScale: '3:4',  // 设置裁剪比例为 3:4
          success: async (res) => {
            try {
              // 转换图片为base64
              const base64 = await this.imageToBase64(res.tempFilePath);

              // 更新本地数据
              this.setData({
                'basicInfo.photoUrl': base64
              });

              // 同时更新本地存储
              const basicInfo = wx.getStorageSync('basicInfo') || {};
              basicInfo.photoUrl = base64;
              wx.setStorageSync('basicInfo', basicInfo);

              wx.showToast({
                title: '图片添加成功',
                icon: 'success'
              });
            } catch (error) {
              console.error('图片转换失败:', error);
              wx.showToast({
                title: '图片处理失败',
                icon: 'none'
              });
            }
          },
          fail: (err) => {
            console.error('裁剪失败:', err);
            wx.showToast({
              title: '裁剪失败',
              icon: 'none'
            });
          }
        });
      },
      fail: (err) => {
        console.error('选择失败:', err);
        wx.showToast({
          title: '选择失败',
          icon: 'none'
        });
      }
    });
  },

  // 图片转base64方法
  imageToBase64(filePath) {
    return new Promise((resolve, reject) => {
      try {
        // 获取文件后缀名
        const ext = filePath.substring(filePath.lastIndexOf('.') + 1).toLowerCase();

        // 根据后缀设置MIME类型
        let mimeType = 'image/jpeg'; // 默认类型
        switch (ext) {
            case 'png':
                mimeType = 'image/png';
                break;
            case 'gif':
                mimeType = 'image/gif';
                break;
            case 'webp':
                mimeType = 'image/webp';
                break;
            case 'jpg':
            case 'jpeg':
                mimeType = 'image/jpeg';
                break;
            default:
                console.log('未知图片类型，使用默认type: image/jpeg');
        }

        // 添加文件类型日志
        console.log('图片类型:', ext, 'MIME类型:', mimeType);

        wx.getFileSystemManager().readFile({
            filePath,
            encoding: 'base64',
            success: res => {
                // 添加详细的日志
                console.log('图片转换成功 ->', {
                    type: mimeType,
                    size: res.data.length,
                    path: filePath
                });
                resolve(`data:${mimeType};base64,` + res.data);
            },
            fail: error => {
                console.error('图片转base64失败:', error);
                reject(error);
            }
        });
      } catch (error) {
        console.error('图片处理过程出错:', error);
        reject(error);
      }
    });
  },

  // 统一处理模块点击事件
  handleModuleClick(e) {
    const moduleType = e.currentTarget.dataset.type;
    let url = '';

    switch (moduleType) {
      case 'basicInfo':
        url = '/pages/makeResume/basicInfo/basicInfo';
        break;
      case 'jobIntention':
        url = '/pages/makeResume/jobIntention/jobIntention';
        break;
      case 'education':
        url = '/pages/makeResume/education/education/education';
        break;
      case 'school':
        url = '/pages/makeResume/school/school/school';
        break;
      case 'internship':
        url = '/pages/makeResume/internship/internshipExperience/internshipExperience';
        break;
      case 'work':
        url = '/pages/makeResume/work/work/work';
        break;
      case 'project':
        url = '/pages/makeResume/project/project/project';
        break;
      case 'skills':
        url = '/pages/makeResume/skills/skills';
        break;
      case 'awards':
        url = '/pages/makeResume/awards/awards';
        break;
      case 'interests':
        url = '/pages/makeResume/interests/interests';
        break;
      case 'evaluation':
        url = '/pages/makeResume/evaluation/evaluation';
        break;
      case 'custom1':
        url = '/pages/makeResume/custom/custom1/custom1';
        break;
      case 'custom2':
        url = '/pages/makeResume/custom/custom2/custom2';
        break;
      case 'custom3':
        url = '/pages/makeResume/custom/custom3/custom3';
        break;
      default:
        console.warn('未知的模块类型:', moduleType);
        return;
    }

    if (url) {
      // 记录用户行为
      if (app && app.trackUserAction) {
        app.trackUserAction('resume_edit', {
          moduleType: moduleType
        });
      }

      wx.navigateTo({ url });
    }
  }
})