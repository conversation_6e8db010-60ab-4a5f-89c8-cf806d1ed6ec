/**
 * 用户行为跟踪工具类
 * 用于记录用户的各种行为，如页面访问、功能使用等
 */
const apiConfig = require('../../config/apiConfig');

// 行为类型常量
const ACTION_TYPES = {
  PAGE_VIEW: 'page_view',         // 页面访问
  LOGIN: 'login',                 // 用户登录
  LOGOUT: 'logout',               // 用户登出
  RESUME_CREATE: 'resume_create', // 创建简历
  RESUME_EDIT: 'resume_edit',     // 编辑简历
  RESUME_PREVIEW: 'resume_preview', // 预览简历
  RESUME_EXPORT: 'resume_export', // 导出简历
  TEMPLATE_SWITCH: 'template_switch', // 切换模板
  STYLE_CHANGE: 'style_change',   // 修改样式
};

// 缓存队列，用于批量上报
let actionQueue = [];
// 是否正在上报中
let isReporting = false;
// 上报间隔时间（毫秒）
const REPORT_INTERVAL = 10000; // 10秒
// 队列最大长度，超过此长度将触发上报
const MAX_QUEUE_LENGTH = 10;
// 定时器ID
let reportTimerId = null;

/**
 * 记录用户行为
 * @param {string} actionType - 行为类型，使用ACTION_TYPES中的常量
 * @param {Object} actionDetail - 行为详情，可选
 * @param {boolean} immediate - 是否立即上报，默认false
 */
function trackUserAction(actionType, actionDetail = {}, immediate = false) {
  // 获取用户ID
  const userId = wx.getStorageSync('userId');
  if (!userId) {
    console.log('未登录用户，行为将不会被记录');
    return;
  }
  
  // 构建行为数据
  const actionData = {
    userId: userId,
    actionType: actionType,
    actionDetail: actionDetail,
    timestamp: new Date().getTime(),
    // 添加设备信息
    deviceInfo: getDeviceInfo()
  };
  
  // 添加到队列
  actionQueue.push(actionData);
  console.log(`行为已添加到队列: ${actionType}`, actionDetail);
  
  // 如果需要立即上报或队列长度达到阈值，则立即上报
  if (immediate || actionQueue.length >= MAX_QUEUE_LENGTH) {
    reportActions();
  } else if (!reportTimerId) {
    // 如果没有定时器，则启动定时上报
    reportTimerId = setTimeout(() => {
      reportActions();
    }, REPORT_INTERVAL);
  }
}

/**
 * 上报行为数据
 */
function reportActions() {
  // 如果队列为空或正在上报中，则返回
  if (actionQueue.length === 0 || isReporting) {
    return;
  }
  
  // 标记为正在上报
  isReporting = true;
  
  // 清除定时器
  if (reportTimerId) {
    clearTimeout(reportTimerId);
    reportTimerId = null;
  }
  
  // 复制当前队列并清空
  const actionsToReport = [...actionQueue];
  actionQueue = [];
  
  // 获取token
  const token = wx.getStorageSync('userToken');
  
  // 发送请求
  wx.request({
    url: apiConfig.batchRecordActionUrl,
    method: 'POST',
    data: {
      actions: actionsToReport
    },
    header: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    },
    success: (res) => {
      if (res.data.success) {
        console.log(`成功上报 ${actionsToReport.length} 条行为数据`);
      } else {
        console.error('上报行为数据失败:', res.data.message);
        // 失败时将数据放回队列
        actionQueue = [...actionsToReport, ...actionQueue];
      }
    },
    fail: (err) => {
      console.error('上报行为数据请求失败:', err);
      // 失败时将数据放回队列
      actionQueue = [...actionsToReport, ...actionQueue];
    },
    complete: () => {
      // 标记为上报完成
      isReporting = false;
      
      // 如果队列中还有数据，则继续上报
      if (actionQueue.length > 0) {
        reportTimerId = setTimeout(() => {
          reportActions();
        }, 5000); // 失败后5秒重试
      }
    }
  });
}

/**
 * 获取设备信息
 * @returns {Object} 设备信息对象
 */
function getDeviceInfo() {
  try {
    const systemInfo = wx.getSystemInfoSync();
    return {
      brand: systemInfo.brand,
      model: systemInfo.model,
      system: systemInfo.system,
      platform: systemInfo.platform,
      SDKVersion: systemInfo.SDKVersion
    };
  } catch (e) {
    console.error('获取设备信息失败:', e);
    return {};
  }
}

/**
 * 页面访问记录
 * @param {string} pagePath - 页面路径
 * @param {Object} options - 页面参数
 */
function trackPageView(pagePath, options = {}) {
  trackUserAction(ACTION_TYPES.PAGE_VIEW, {
    pagePath: pagePath,
    options: options
  });
}

/**
 * 记录简历预览
 * @param {string} resumeId - 简历ID
 * @param {string} templateId - 模板ID
 */
function trackResumePreview(resumeId, templateId) {
  trackUserAction(ACTION_TYPES.RESUME_PREVIEW, {
    resumeId: resumeId,
    templateId: templateId
  });
}

/**
 * 记录简历导出
 * @param {string} resumeId - 简历ID
 * @param {string} exportType - 导出类型，如'pdf'
 */
function trackResumeExport(resumeId, exportType) {
  trackUserAction(ACTION_TYPES.RESUME_EXPORT, {
    resumeId: resumeId,
    exportType: exportType
  }, true); // 导出操作立即上报
}

// 导出工具类和常量
module.exports = {
  ACTION_TYPES,
  trackUserAction,
  trackPageView,
  trackResumePreview,
  trackResumeExport,
  reportActions // 导出此方法用于手动触发上报
};
