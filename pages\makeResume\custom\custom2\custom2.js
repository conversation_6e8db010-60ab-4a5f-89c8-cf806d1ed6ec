Page({
  data: {
    custom2FormData: {
      index: '2',
      startDate: '',
      endDate: '',
      customName: '',
      role: '',
      content: ''
    },
    customFormStyle: {
      isBold: false,
      isItalic: false,
      isUnderline: false,
      isList: false
    }
  },

  onLoad(options) {
    // 获取保存的数据
    const savedData = wx.getStorageSync('customContent2') || {};
    
    this.setData({
      'custom2FormData.startDate': savedData.startDate || '',
      'custom2FormData.endDate': savedData.endDate || '',
      'custom2FormData.customName': savedData.customName || '',
      'custom2FormData.role': savedData.role || '',
      'custom2FormData.content': savedData.content || ''
    });
  },

  // 处理开始日期变化
  handleStartDateChange(e) {
    this.setData({
      'custom2FormData.startDate': e.detail.value
    });
  },

  // 处理结束日期变化
  handleEndDateChange(e) {
    this.setData({
      'custom2FormData.endDate': e.detail.value
    });
  },

  // 设置"至今"
  setToNow() {
    this.setData({
      'custom2FormData.endDate': '至今'
    });
  },

  // 处理名称输入
  handleNameInput(e) {
    this.setData({
      'custom2FormData.customName': e.detail.value
    });
  },

  // 处理角色输入
  handleRoleInput(e) {
    this.setData({
      'custom2FormData.role': e.detail.value
    });
  },

  // 处理内容输入
  handleContentInput(e) {
    this.setData({
      'custom2FormData.content': e.detail.value
    });
  },

  // 文本编辑工具栏功能
  handleBold() {
    this.setData({ 
      'customFormStyle.isBold': !this.data.customFormStyle.isBold 
    });
  },

  handleItalic() {
    this.setData({ 
      'customFormStyle.isItalic': !this.data.customFormStyle.isItalic 
    });
  },

  handleUnderline() {
    this.setData({ 
      'customFormStyle.isUnderline': !this.data.customFormStyle.isUnderline 
    });
  },

  handleList() {
    this.setData({ 
      'customFormStyle.isList': !this.data.customFormStyle.isList 
    });
  },

  // 保存内容
  saveContent() {
    const { custom2FormData } = this.data;
    const { 
      index, 
      startDate, 
      endDate, 
      customName, 
      role, 
      content 
    } = custom2FormData;

    // 添加必填校验
    if (!customName || customName.trim() === '') {
      wx.showToast({
        title: '请输入名称',
        icon: 'none'
      });
      return;
    }

    const saveData = {
      startDate,
      endDate,
      customName,
      role,
      content
    };

    wx.setStorageSync(`customContent${index}`, saveData);
    wx.setStorageSync(`customList${index}`, [saveData]);

    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      }
    });
  },

  // 删除内容
  deleteContent() {
    wx.showModal({
      title: '提示',
      content: '确定要删除内容吗？',
      success: (res) => {
        if (res.confirm) {
          const { index } = this.data.custom2FormData;
          wx.removeStorageSync(`customContent${index}`);
          wx.removeStorageSync(`customList${index}`);
          this.setData({
            custom2FormData: {
              ...this.data.custom2FormData,
              startDate: '',
              endDate: '',
              customName: '',
              role: '',
              content: ''
            }
          });
          
          wx.showToast({
            title: '已删除',
            icon: 'success',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 2000);
            }
          });
        }
      }
    });
  }
}); 