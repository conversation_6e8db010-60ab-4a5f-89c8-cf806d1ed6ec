/* pages/user/records/records.wxss */
.container {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 记录列表样式 */
.records-list {
  width: 100%;
}

.record-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.record-type {
  margin-bottom: 16rpx;
}

.record-type-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.record-content {
  display: flex;
  flex-direction: column;
}

.record-detail {
  display: flex;
  flex-direction: column;
  margin-bottom: 10rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
}

.record-time {
  font-size: 24rpx;
  color: #999;
  align-self: flex-end;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 20rpx 0;
}

.load-more text {
  font-size: 28rpx;
  color: #4B8BF5;
}

.no-more {
  text-align: center;
  padding: 20rpx 0;
}

.no-more text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4B8BF5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}
