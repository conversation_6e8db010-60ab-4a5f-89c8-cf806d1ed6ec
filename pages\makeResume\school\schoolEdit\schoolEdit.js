Page({
  data: {
    schoolEditFormData: {
      role: '',           // 担任角色
      startDate: '',      // 开始时间
      endDate: '',        // 结束时间
      content: ''         // 经历描述
    },
    editIndex: -1,
    editorCtx: null,      // 富文本编辑器上下文
    formats: {}           // 记录当前选择的文本格式
  },

  onLoad(options) {
    if (options.index) {
      const schoolList = wx.getStorageSync('schoolList') || [];
      const editIndex = parseInt(options.index);

      if (schoolList[editIndex]) {
        this.setData({
          schoolEditFormData: schoolList[editIndex],
          editIndex: editIndex
        });
      }
    }
  },

  // 初始化编辑器
  onEditorReady() {
    wx.createSelectorQuery()
      .select('#editor')
      .context((res) => {
        this.editorCtx = res.context;
        // 如果有已存在的内容，设置到编辑器
        if (this.data.schoolEditFormData.content) {
          this.editorCtx.setContents({
            html: this.data.schoolEditFormData.content
          });
        }
      })
      .exec();
  },

  // 处理输入框内容变化
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`schoolEditFormData.${field}`]: value
    });
  },

  // 处理编辑器内容变化
  handleEditorInput(e) {
    this.setData({
      'schoolEditFormData.content': e.detail.html
    });
  },

  // 设置文本格式
  handleFormat(e) {
    const { name } = e.currentTarget.dataset;
    if (!this.editorCtx) return;

    switch (name) {
      case 'bold':
        this.editorCtx.format('bold');
        break;
      case 'italic':
        this.editorCtx.format('italic');
        break;
      case 'underline':
        this.editorCtx.format('underline');
        break;
      case 'list':
        this.editorCtx.format('list', 'bullet');
        break;
      case 'indent':
        this.editorCtx.format('indent');
        break;
    }
  },

  // 处理日期选择
  handleDateChange(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;

    this.setData({
      [`schoolEditFormData.${field}`]: value
    });
  },

  // 设置结束日期为"至今"
  setEndDateToNow() {
    this.setData({
      'schoolEditFormData.endDate': '至今'
    });
  },

  // 保存在校经历
  saveSchool() {
    const { schoolEditFormData, editIndex } = this.data;

    // 验证必填字段
    if (!schoolEditFormData.role || !schoolEditFormData.startDate) {
      wx.showToast({
        title: '请填写必填信息',
        icon: 'none'
      });
      return;
    }

    // 获取已保存的数据
    const schoolList = wx.getStorageSync('schoolList') || [];

    if (editIndex >= 0) {
      // 更新已有记录
      schoolList[editIndex] = schoolEditFormData;
    } else {
      // 添加新记录
      schoolList.push(schoolEditFormData);
    }

    // 保存更新后的数据
    wx.setStorageSync('schoolList', schoolList);

    // 同时保存到resumeManager
    const resumeManager = require('../../../../utils/resume/resumeManager.js');
    const currentResumeData = resumeManager.getCurrentResumeData() || {};

    // 更新在校经历
    currentResumeData.schools = schoolList;

    // 保存到resumeManager
    const success = resumeManager.saveCurrentResumeData(currentResumeData);
    console.log('在校经历保存到resumeManager结果:', success);

    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500,
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  }
});