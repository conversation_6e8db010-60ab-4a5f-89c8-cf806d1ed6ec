/**
 * 自动登录工具类
 * 用于在小程序启动时自动执行微信登录流程
 */
const userApi = require('../api/userApi');

/**
 * 执行自动登录
 * @returns {Promise<boolean>} 登录是否成功
 */
function performAutoLogin() {
  return new Promise((resolve, reject) => {
    console.log('开始执行自动登录...');

    // 检查是否已有有效的token
    const existingToken = wx.getStorageSync('userToken');
    const existingUserId = wx.getStorageSync('userId');

    if (existingToken && existingUserId) {
      console.log('检测到已有登录信息，验证token有效性...');
      // 验证现有token是否有效
      validateExistingToken(existingToken)
        .then((isValid) => {
          if (isValid) {
            console.log('现有token有效，自动登录成功');
            resolve(true);
          } else {
            console.log('现有token无效，重新登录...');
            executeWxLogin().then(resolve).catch(reject);
          }
        })
        .catch(() => {
          console.log('token验证失败，重新登录...');
          executeWxLogin().then(resolve).catch(reject);
        });
    } else {
      console.log('未检测到登录信息，执行微信登录...');
      executeWxLogin().then(resolve).catch(reject);
    }
  });
}

/**
 * 验证现有token是否有效
 * @param {string} token - 用户token
 * @returns {Promise<boolean>} token是否有效
 */
function validateExistingToken(token) {
  return userApi.validateToken()
    .then(() => true)
    .catch(() => false);
}

/**
 * 执行微信登录流程
 * @returns {Promise<boolean>} 登录是否成功
 */
function executeWxLogin() {
  return new Promise((resolve, reject) => {
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          console.log('获取微信登录code成功:', loginRes.code);

          // 使用API模块发送登录请求
          userApi.login(loginRes.code, true, false)
            .then((res) => {
              console.log('自动登录成功');

              // 保存登录信息
              const { token, userId, membershipInfo } = res;
              wx.setStorageSync('userToken', token);
              wx.setStorageSync('userId', userId);

              // 保存会员信息
              if (membershipInfo) {
                wx.setStorageSync('membershipInfo', membershipInfo);
              }

              // 更新全局状态
              const app = getApp();
              if (app) {
                app.globalData.userToken = token;
                app.globalData.userId = userId;
                app.globalData.hasUserInfo = true;
                if (membershipInfo) {
                  app.globalData.isMember = membershipInfo.isMember || false;
                  app.globalData.membershipExpiry = membershipInfo.expiry || null;
                }
              }

              resolve(true);
            })
            .catch((err) => {
              console.error('自动登录失败:', err);
              resolve(false);
            });
        } else {
          console.error('获取微信登录code失败:', loginRes);
          resolve(false);
        }
      },
      fail: (err) => {
        console.error('wx.login调用失败:', err);
        resolve(false);
      }
    });
  });
}

/**
 * 静默登录（不显示任何提示）
 * @returns {Promise<boolean>} 登录是否成功
 */
function silentLogin() {
  return performAutoLogin();
}

/**
 * 检查登录状态
 * @returns {boolean} 是否已登录
 */
function checkLoginStatus() {
  const token = wx.getStorageSync('userToken');
  const userId = wx.getStorageSync('userId');
  return !!(token && userId);
}

/**
 * 获取用户ID
 * @returns {string|null} 用户ID
 */
function getUserId() {
  return wx.getStorageSync('userId') || null;
}

/**
 * 获取用户Token
 * @returns {string|null} 用户Token
 */
function getUserToken() {
  return wx.getStorageSync('userToken') || null;
}



module.exports = {
  performAutoLogin,
  silentLogin,
  checkLoginStatus,
  getUserId,
  getUserToken,
  validateExistingToken
};
